// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.7
package sys

import (
	"context"
	"fmt"
	"hotgo/addons/usercenter/api/admin/usercenteruserreport"
	usercontexts "hotgo/addons/usercenter/library/contexts"
	"hotgo/addons/usercenter/model/input/sysin"
	"hotgo/addons/usercenter/service"
	"hotgo/internal/dao"
	"hotgo/internal/library/contexts"
	"hotgo/internal/library/hgorm"
	"hotgo/internal/library/hgorm/handler"
	"hotgo/internal/logic/common"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	isc "hotgo/internal/service"
	"hotgo/utility/convert"
	"hotgo/utility/excel"
	"hotgo/utility/file"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/xuri/excelize/v2"
)

type sSysUsercenterUserReport struct {
	// 导出配置
	BatchSize       int    // 每批处理数量
	ExportName      string // 导出名称
	TaskType        string // 任务类型
	ModuleName      string // 模块名称
	SheetNamePrefix string // Excel工作表名前缀
}

func NewSysUsercenterUserReport() *sSysUsercenterUserReport {
	return &sSysUsercenterUserReport{
		BatchSize:       50000,
		ExportName:      "用户报表",
		TaskType:        "usercenter_user_report",
		ModuleName:      "usercenter_user_report",
		SheetNamePrefix: "用户报表数据",
	}
}

func init() {
	service.RegisterSysUsercenterUserReport(NewSysUsercenterUserReport())
	// 新增这句 - 直接使用实例方法
	common.RegisterExportProcessor("usercenter_user_report", NewSysUsercenterUserReport().processUsercenterOrderExport)
	common.RegisterExportProcessor("usercenter_user_report_user", NewSysUsercenterUserReport().processUserExportTask)
}

// ExportBigData 大数据异步导出代收订单
func (s *sSysUsercenterUserReport) ExportBigData(ctx context.Context, in *sysin.UsercenterUserReportListInp) (err error) {
	// 获取当前用户信息
	user := contexts.GetUser(ctx)
	if user == nil {
		return gerror.New("用户未登录")
	}

	// 重新获取精确的总记录数
	in.Page = 1
	in.PerPage = 1
	_, totalCount, _, err := s.List(ctx, in)
	if err != nil {
		return gerror.Wrap(err, "获取总记录数失败")
	}

	// 构建查询参数JSON
	queryParamsJson, err := gjson.Encode(in)
	if err != nil {
		return gerror.Wrap(err, "序列化查询参数失败")
	}

	// 计算文件过期时间（7天后）
	expireTime := gtime.Now().Add(7 * 24 * time.Hour)

	// 构建任务数据
	taskData := &entity.SysExportTask{
		TaskName:         fmt.Sprintf("%s导出-%s", s.ExportName, gtime.Now().Format("Y-m-d H:i:s")),
		TaskType:         s.TaskType,
		ModuleName:       s.ModuleName,
		Status:           "pending",
		Progress:         0,
		TotalRecords:     uint64(totalCount),
		ProcessedRecords: 0,
		FileCount:        0,
		FileSize:         0,
		QueryParams:      string(queryParamsJson),
		FilePaths:        "",
		DownloadUrl:      "",
		ExpireTime:       expireTime,
		StartTime:        nil,
		EndTime:          nil,
		ErrorMessage:     "",
		RetryCount:       0,
		CreatedBy:        uint64(user.Id),
		CreatedByName:    user.RealName,
		CreatedAt:        gtime.Now(),
		UpdatedAt:        gtime.Now(),
	}

	// 插入任务记录
	result, err := dao.SysExportTask.Ctx(ctx).Data(taskData).Insert()
	if err != nil {
		return gerror.Wrap(err, "创建导出任务失败")
	}

	// 获取任务ID
	taskId, err := result.LastInsertId()
	if err != nil {
		return gerror.Wrap(err, "获取任务ID失败")
	}

	g.Log().Infof(ctx, "创建大数据导出任务成功，任务ID: %d, 总记录数: %d", taskId, totalCount)

	// TODO: 启动后台异步处理
	// go s.processExportTask(ctx, taskId, in)

	// 重定向到进度页面
	r := ghttp.RequestFromCtx(ctx)
	if r != nil {
		// 获取当前请求的token，传递给进度页面
		token := r.Get("authorization").String()
		if token == "" {
			token = r.Header.Get("Authorization")
			token = gstr.Replace(token, "Bearer ", "")
		}

		progressUrl := fmt.Sprintf("/html/export-progress.html?taskId=%d&authorization=%s", taskId, token)
		r.Response.RedirectTo(progressUrl)
		return nil
	}

	// 简单返回成功，前端会在新窗口显示进度页面
	return nil
}

// ExportBigData 大数据异步导出代收订单
func (s *sSysUsercenterUserReport) UserExportBigData(ctx context.Context, in *sysin.UsercenterUserReportListInp) (err error) {
	// 获取当前用户信息
	user := usercontexts.GetUser(ctx)
	if user == nil {
		return gerror.New("用户未登录")
	}

	// 重新获取精确的总记录数
	in.Page = 1
	in.PerPage = 1
	_, totalCount, _, err := s.List(ctx, in)
	if err != nil {
		return gerror.Wrap(err, "获取总记录数失败")
	}

	// 构建查询参数JSON
	queryParamsJson, err := gjson.Encode(in)
	if err != nil {
		return gerror.Wrap(err, "序列化查询参数失败")
	}

	// 计算文件过期时间（7天后）
	expireTime := gtime.Now().Add(7 * 24 * time.Hour)

	// 构建任务数据
	taskData := &entity.SysExportTask{
		TaskName:         fmt.Sprintf("%s导出-%s", s.ExportName, gtime.Now().Format("Y-m-d H:i:s")),
		TaskType:         s.TaskType + "_user",
		ModuleName:       s.ModuleName,
		Status:           "pending",
		Progress:         0,
		TotalRecords:     uint64(totalCount),
		ProcessedRecords: 0,
		FileCount:        0,
		FileSize:         0,
		QueryParams:      string(queryParamsJson),
		FilePaths:        "",
		DownloadUrl:      "",
		ExpireTime:       expireTime,
		StartTime:        nil,
		EndTime:          nil,
		ErrorMessage:     "",
		RetryCount:       0,
		CreatedBy:        10000 + uint64(user.Id),
		CreatedByName:    "Merchant_" + user.RealName,
		CreatedAt:        gtime.Now(),
		UpdatedAt:        gtime.Now(),
	}

	// 插入任务记录
	result, err := dao.SysExportTask.Ctx(ctx).Data(taskData).Insert()
	if err != nil {
		return gerror.Wrap(err, "创建导出任务失败")
	}

	// 获取任务ID
	taskId, err := result.LastInsertId()
	if err != nil {
		return gerror.Wrap(err, "获取任务ID失败")
	}

	g.Log().Infof(ctx, "创建大数据导出任务成功，任务ID: %d, 总记录数: %d", taskId, totalCount)

	// 启动后台异步处理
	go func() {
		if err := s.processUserExportTask(gctx.New(), taskId, string(queryParamsJson)); err != nil {
			g.Log().Errorf(ctx, "商户端大数据导出任务 %d 处理失败: %v", taskId, err)
		}
	}()

	// 重定向到进度页面
	r := ghttp.RequestFromCtx(ctx)
	if r != nil {
		// 获取商户端token
		var token string
		if userToken := r.Header.Get("ba-user-token"); userToken != "" {
			token = userToken
		} else {
			token = r.Get("ba-user-token").String()
		}

		// 构建商户端进度页面URL
		progressUrl := fmt.Sprintf("/html/export-progress.html?taskId=%d&ba-user-token=%s&userType=merchant", taskId, token)
		r.Response.RedirectTo(progressUrl)
		return nil
	}

	// 简单返回成功，前端会在新窗口显示进度页面
	return nil
}

// 真正的导出实现
func (s *sSysUsercenterUserReport) processUsercenterOrderExport(ctx context.Context, taskId int64, params string) error {
	// ==================== 类型配置区 - 复用时只需修改这部分 ====================
	// 类型配置 - 复用时需要修改的类型
	type (
		ListInputType    = sysin.UsercenterUserReportListInp     // 列表查询输入类型
		ListOutputType   = sysin.UsercenterUserReportListModel   // 列表输出类型
		ExportOutputType = sysin.UsercenterUserReportExportModel // 导出输出类型
	)

	// 服务方法配置 - 复用时需要修改的方法调用
	listFunc := func(ctx context.Context, in *ListInputType) ([]*ListOutputType, int, error) {
		list, totalCount, _, err := s.List(ctx, in)
		return list, totalCount, err
	}
	// ==================== 配置区结束 ====================

	g.Log().Infof(ctx, "========== 开始处理大数据导出任务 %d ==========", taskId)

	// 1. 解析参数
	var in ListInputType
	if err := gjson.Unmarshal([]byte(params), &in); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 解析导出参数失败: %v", taskId, err)
		return gerror.Wrap(err, "解析导出参数失败")
	}
	g.Log().Infof(ctx, "任务 %d: 参数解析成功: %+v", taskId, in)

	// 2. 获取总记录数（用于计算分批次数和进度）
	// 首先更新进度为1% - 开始查询总记录数
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 1, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新初始进度失败: %v", taskId, err)
	}
	g.Log().Infof(ctx, "任务 %d: 开始查询总记录数...", taskId)

	in.Page = 1
	in.PerPage = 1
	_, totalCount, err := listFunc(ctx, &in)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 查询总记录数失败: %v", taskId, err)
		return gerror.Wrap(err, "查询总记录数失败")
	}
	g.Log().Infof(ctx, "任务 %d: 查询到总记录数: %d 条", taskId, totalCount)

	// 总记录数查询完成，更新进度为3%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 3, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新总记录数查询进度失败: %v", taskId, err)
	}

	if totalCount == 0 {
		g.Log().Warningf(ctx, "任务 %d: 没有数据需要导出，直接完成任务", taskId)
		return common.NewCommonExport().CompleteTask(ctx, taskId, []string{}, "", 0, 0)
	}

	// 每批处理5000条记录，生成一个Excel文件
	totalPages := (totalCount + s.BatchSize - 1) / s.BatchSize // 计算总批次

	g.Log().Infof(ctx, "任务 %d: 分批策略 - 总记录数=%d, 每批大小=%d, 总文件数=%d",
		taskId, totalCount, s.BatchSize, totalPages)

	// 4. 准备文件存储目录
	// 更新进度为5% - 开始准备导出环境
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 5, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新环境准备进度失败: %v", taskId, err)
		return gerror.Wrap(err, "获取上传配置失败")
	}
	g.Log().Infof(ctx, "任务 %d: 开始准备导出环境...", taskId)

	serverRoot := g.Cfg().MustGet(ctx, "server.serverRoot", "resource/public").String()
	uploadConfig, err := isc.SysConfig().GetUpload(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 获取上传配置失败: %v", taskId, err)
		return gerror.Wrap(err, "获取上传配置失败")
	}

	exportDir := serverRoot + "/" + strings.Trim(uploadConfig.LocalPath, "/") + "/export/" + fmt.Sprintf("task_%d/", taskId)
	if !gfile.Exists(exportDir) {
		if err = gfile.Mkdir(exportDir); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 创建导出目录失败: %v, 目录: %s", taskId, err, exportDir)
			return gerror.Wrap(err, "创建导出目录失败")
		}
	}
	g.Log().Infof(ctx, "任务 %d: 导出目录创建成功: %s", taskId, exportDir)

	// 环境准备完成，更新进度为8%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 8, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新环境准备完成进度失败: %v", taskId, err)
	}

	var excelFiles []string // 收集所有生成的Excel文件路径
	var processedRecords int64 = 0

	// 5. 分批处理数据，每批生成一个Excel文件
	for page := 1; page <= totalPages; page++ {
		g.Log().Infof(ctx, "任务 %d: ========== 开始处理第 %d/%d 批 ==========", taskId, page, totalPages)

		// 更新进度 - 开始处理当前批次（8% + 85% * 当前批次进度）
		batchStartProgress := 8 + int(float64(page-1)/float64(totalPages)*85)
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchStartProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批开始进度失败: %v", taskId, page, err)
		}

		// 设置分页参数
		in.Page = page
		in.PerPage = s.BatchSize

		// 查询当前批次数据
		g.Log().Infof(ctx, "任务 %d: 查询第 %d 批数据，页码=%d，每页=%d", taskId, page, in.Page, in.PerPage)
		list, currentCount, err := listFunc(ctx, &in)
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 查询第%d批数据失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("查询第%d批数据失败", page))
		}
		g.Log().Infof(ctx, "任务 %d: 第 %d 批查询成功，获得 %d 条记录", taskId, page, currentCount)

		// 数据查询完成，更新批次内细粒度进度（+1%）
		batchDataProgress := batchStartProgress + 1
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchDataProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据查询进度失败: %v", taskId, page, err)
		}

		// 转换为导出结构体
		var exports []ExportOutputType
		if err := gconv.Scan(list, &exports); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批数据结构转换失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批数据结构转换失败", page))
		}
		g.Log().Infof(ctx, "任务 %d: 第 %d 批数据转换成功，准备生成Excel文件", taskId, page)

		// 6. 为当前批次创建独立的Excel文件
		f := excelize.NewFile()
		sheetName := fmt.Sprintf("%s_%d", s.SheetNamePrefix, page)
		f.SetSheetName("Sheet1", sheetName)

		// 设置表头
		tags, err := convert.GetEntityDescTags(ExportOutputType{})
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批获取导出字段失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批获取导出字段失败", page))
		}

		// 7. 写入当前批次的数据 - 优化为真正的批量写入
		g.Log().Infof(ctx, "任务 %d: 开始准备第 %d 批数据进行批量写入", taskId, page)

		// Excel文件准备完成，更新批次内细粒度进度（+1%）
		batchExcelPrepProgress := batchDataProgress + 1
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchExcelPrepProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批Excel准备进度失败: %v", taskId, page, err)
		}

		// 预先构造所有行数据
		allRows := make([][]interface{}, len(exports))
		for i, export := range exports {
			// 使用反射获取结构体字段值
			t := reflect.TypeOf(export)
			value := reflect.ValueOf(export)
			row := make([]interface{}, 0, t.NumField()) // 预分配容量

			for j := 0; j < t.NumField(); j++ {
				val := value.Field(j).Interface()
				row = append(row, val)
			}

			allRows[i] = row
			processedRecords++

			// 每处理1000条记录打印一次进度并更新数据库进度
			if (i+1)%1000 == 0 {
				g.Log().Infof(ctx, "任务 %d: 第 %d 批已准备 %d/%d 条记录", taskId, page, i+1, len(exports))
				// 数据准备过程中的细粒度进度更新
				dataProgress := batchExcelPrepProgress + int(float64(i+1)/float64(len(exports))*2) // 数据准备占2%
				if err := common.NewCommonExport().UpdateProgress(ctx, taskId, dataProgress, processedRecords); err != nil {
					g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据准备进度失败: %v", taskId, page, err)
				}
			}
		}

		// 数据准备完成，更新进度
		batchDataPreparedProgress := batchExcelPrepProgress + 2
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchDataPreparedProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据准备完成进度失败: %v", taskId, page, err)
		}

		// 使用高效的批量写入方法
		g.Log().Infof(ctx, "任务 %d: 开始使用StreamWriter批量写入表头和 %d 条数据", taskId, len(allRows))

		// 创建流式写入器以提高大批量数据写入性能
		streamWriter, err := f.NewStreamWriter(sheetName)
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 创建StreamWriter失败: %v", taskId, err)
			return gerror.Wrap(err, "创建StreamWriter失败")
		}

		// 首先写入表头到StreamWriter
		headerRow := make([]interface{}, len(tags))
		for i, tag := range tags {
			headerRow[i] = tag
		}
		if err := streamWriter.SetRow("A1", headerRow); err != nil {
			streamWriter.Flush()
			g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入表头失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入表头失败", page))
		}

		// 设置表头样式（在StreamWriter之外设置样式）
		headerStyle, _ := f.NewStyle(`{
			"font":{"bold":true,"color":"#000000","size":12},
			"fill":{"type":"pattern","color":["#E0E0E0"],"pattern":1},
			"alignment":{"horizontal":"center","vertical":"center"},
			"border":[{"type":"left","color":"#000000","style":1},{"type":"top","color":"#000000","style":1},{"type":"bottom","color":"#000000","style":1},{"type":"right","color":"#000000","style":1}]
		}`)

		// 使用流式写入批量写入所有数据
		for i, row := range allRows {
			rowNum := i + 2 // 从第2行开始（第1行是表头）
			cellRange := fmt.Sprintf("A%d", rowNum)
			if err := streamWriter.SetRow(cellRange, row); err != nil {
				streamWriter.Flush()
				g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入第%d行数据失败: %v", taskId, page, rowNum, err)
				return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入第%d行数据失败", page, rowNum))
			}

			// 每写入1000行更新一次进度
			if (i+1)%1000 == 0 {
				writeProgress := batchDataPreparedProgress + int(float64(i+1)/float64(len(allRows))*2) // Excel写入占2%
				if err := common.NewCommonExport().UpdateProgress(ctx, taskId, writeProgress, processedRecords); err != nil {
					g.Log().Errorf(ctx, "任务 %d: 更新第%d批Excel写入进度失败: %v", taskId, page, err)
				}
			}
		}

		// 完成流式写入
		if err := streamWriter.Flush(); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入flush失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入flush失败", page))
		}

		// 在StreamWriter完成后设置表头样式
		headerRange := fmt.Sprintf("A1:%s1", string(rune('A'+len(tags)-1)))
		if err := f.SetCellStyle(sheetName, "A1", headerRange, headerStyle); err != nil {
			g.Log().Warningf(ctx, "任务 %d: 第%d批设置表头样式失败: %v", taskId, page, err)
			// 样式设置失败不影响数据导出，只记录警告
		}

		g.Log().Infof(ctx, "任务 %d: 第 %d 批数据流式写入完成，共写入表头+%d条记录", taskId, page, len(allRows))

		// Excel写入完成，更新进度
		batchWriteCompleteProgress := batchDataPreparedProgress + 2
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchWriteCompleteProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批写入完成进度失败: %v", taskId, page, err)
		}

		// 8. 保存当前批次的Excel文件
		fileName := fmt.Sprintf(s.ExportName+"_第%d批_共%d条.xlsx", page, len(exports))
		filePath := exportDir + fileName

		if err := f.SaveAs(filePath); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 保存第%d批Excel文件失败: %v, 路径: %s", taskId, page, err, filePath)
			return gerror.Wrap(err, fmt.Sprintf("保存第%d批Excel文件失败", page))
		}

		excelFiles = append(excelFiles, filePath)
		g.Log().Infof(ctx, "任务 %d: 第 %d 批Excel文件生成成功: %s，包含 %d 条记录",
			taskId, page, fileName, len(exports))

		// 9. 更新任务进度 - 当前批次完全处理完成
		progress := 8 + int(float64(page)/float64(totalPages)*85) // 8% 初始化 + 85% 数据处理
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, progress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新进度失败: %v", taskId, err)
		}

		g.Log().Infof(ctx, "任务 %d: 第 %d 批处理完成，已处理 %d/%d 条记录，进度 %d%%",
			taskId, page, processedRecords, totalCount, progress)

		// 适当休眠，避免数据库和磁盘压力
		if page < totalPages {
			time.Sleep(200 * time.Millisecond)
		}
	}

	g.Log().Infof(ctx, "任务 %d: ========== 所有Excel文件生成完成，开始ZIP打包 ==========", taskId)
	g.Log().Infof(ctx, "任务 %d: 共生成 %d 个Excel文件，总记录数 %d 条", taskId, len(excelFiles), processedRecords)

	// 10. 打包所有Excel文件为ZIP
	zipFileName := fmt.Sprintf(s.ExportName+"_任务%d_共%d条记录.zip", taskId, processedRecords)
	zipFilePath := exportDir + zipFileName

	err = file.CreateZipFromFiles(excelFiles, zipFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: ZIP打包失败: %v", taskId, err)
		return gerror.Wrap(err, "ZIP打包失败")
	}

	// 获取ZIP文件大小
	zipFileInfo, err := gfile.Stat(zipFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 获取ZIP文件信息失败: %v", taskId, err)
		return gerror.Wrap(err, "获取ZIP文件信息失败")
	}

	g.Log().Infof(ctx, "任务 %d: ZIP文件打包成功: %s，文件大小: %.2fMB",
		taskId, zipFileName, float64(zipFileInfo.Size())/1024/1024)

	// 12. 清理原始Excel文件，节省磁盘空间
	g.Log().Infof(ctx, "任务 %d: 开始清理原始Excel文件...", taskId)
	deletedCount := 0
	for _, excelFile := range excelFiles {
		if err := gfile.Remove(excelFile); err != nil {
			g.Log().Warningf(ctx, "任务 %d: 删除Excel文件失败: %s, error: %v", taskId, excelFile, err)
		} else {
			deletedCount++
			g.Log().Debugf(ctx, "任务 %d: 已删除Excel文件: %s", taskId, filepath.Base(excelFile))
		}
	}
	g.Log().Infof(ctx, "任务 %d: Excel文件清理完成，成功删除 %d/%d 个文件",
		taskId, deletedCount, len(excelFiles))

	// 更新进度到100%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 100, processedRecords); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新最终进度失败: %v", taskId, err)
	}

	// 11. 完成任务，传递ZIP文件路径和下载URL - 修复URL构建逻辑
	// 构建相对于静态根目录的路径
	relativePath := strings.Trim(uploadConfig.LocalPath, "/") + "/export/" + fmt.Sprintf("task_%d/", taskId) + zipFileName
	downloadUrl := "/" + relativePath

	g.Log().Infof(ctx, "任务 %d: ========== 导出任务完成 ==========", taskId)
	g.Log().Infof(ctx, "任务 %d: 文件信息 - ZIP文件:%s, 文件大小:%.2fMB",
		taskId, zipFileName, float64(zipFileInfo.Size())/1024/1024)
	g.Log().Infof(ctx, "任务 %d: 路径信息 - 物理路径:%s, 下载地址:%s",
		taskId, zipFilePath, downloadUrl)
	g.Log().Infof(ctx, "任务 %d: 统计信息 - 总记录数:%d, Excel文件数:%d",
		taskId, processedRecords, len(excelFiles))

	return common.NewCommonExport().CompleteTask(ctx, taskId, []string{zipFilePath}, downloadUrl, len(excelFiles), zipFileInfo.Size())
}

// Model 用户日报ORM模型
func (s *sSysUsercenterUserReport) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.UsercenterUserReport.Ctx(ctx), option...)
}

// List 获取用户日报列表
func (s *sSysUsercenterUserReport) List(ctx context.Context, in *sysin.UsercenterUserReportListInp) (list []*sysin.UsercenterUserReportListModel, totalCount int, odRes *usercenteruserreport.OdRes, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.FieldsPrefix(dao.UsercenterUserReport.Table(), sysin.UsercenterUserReportListModel{})
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.UsercenterUserReportListModel{}, &dao.UsercenterUser, "usercenterUser"))

	// 关联表字段
	mod = mod.LeftJoinOnFields(dao.UsercenterUser.Table(), dao.UsercenterUserReport.Columns().UserId, "=", dao.UsercenterUser.Columns().Id)
	dfsearch := false
	// 查询报表日期
	if in.ReportDate != nil {
		fmt
		mod = mod.Where(dao.UsercenterUserReport.Columns().ReportDate, in.ReportDate.Format("Y-m-d"))
		dfsearch = true
	}

	// 查询用户名
	if in.UsercenterUserUsername != "" {
		mod = mod.WherePrefixLike(dao.UsercenterUser.Table(), dao.UsercenterUser.Columns().Username, in.UsercenterUserUsername)
		dfsearch = true
	}

	// 查询用户id
	if in.UserId > 0 {
		mod = mod.Where(dao.UsercenterUserReport.Columns().UserId, in.UserId)
		dfsearch = true
	}

	// 查询报表日期范围
	if len(in.ReportDateRange) > 0 {
		mod = mod.Where(dao.UsercenterUserReport.Columns().ReportDate, ">=", in.ReportDateRange[0].Format("Y-m-d"))
		mod = mod.Where(dao.UsercenterUserReport.Columns().ReportDate, in.ReportDateRange[1].Format("Y-m-d"))
		dfsearch = true
	}
	fmt.Printf("today的年月日:%s\n", gtime.Now().Format("Y-m-d"))
	if !dfsearch {
		// 获取通用配置中的数据模式
		commonConfig, err := isc.SysConfig().GetCommon(ctx)
		if err == nil && commonConfig != nil {
			// 如果是节能模式(dataMode=1)，默认显示今天的数据
			if commonConfig.DataMode == 1 {
				// 获取今天的起始和结束时间
				fmt.Printf("today的年月日:%s\n", gtime.Now().Format("Y-m-d"))
				mod = mod.Where(dao.UsercenterUserReport.Columns().ReportDate, gtime.Now().Format("Y-m-d"))
			}
			// 如果是全量模式(dataMode=2)，不添加时间筛选，显示所有数据
		}
	}
	// 分页
	mod = mod.Page(in.Page, in.PerPage)
	fmt.Printf("order:%s columnKey:%s\n", in.Order, in.ColumnKey)

	if in.Order != "" && in.ColumnKey != "" {
		// 验证排序字段是否合法
		validColumns := map[string]string{
			"dsNum":          "ds_num",
			"dsSuccess":      "ds_success",
			"dsMoney":        "ds_money",
			"dsSuccessMoney": "ds_success_money",
			"dsFee":          "ds_fee",
			"dfNum":          "df_num",
			"dfSuccess":      "df_success",
			"dfMoney":        "df_money",
			"dfSuccessMoney": "df_success_money",
			"dfFee":          "df_fee",
			"dfDcl":          "df_dcl",
			"dfYcl":          "df_ycl",
			"dfDclNum":       "df_dcl_num",
		}

		if dbField, ok := validColumns[in.ColumnKey]; ok {
			orderField := dao.UsercenterUserReport.Table() + "." + dbField
			// 先按报表日期排序，再按指定字段排序
			if in.Order == "desc" {
				mod = mod.Order(fmt.Sprintf("%s DESC, %s DESC",
					dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().ReportDate,
					orderField))
			} else {
				mod = mod.Order(fmt.Sprintf("%s DESC, %s ASC",
					dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().ReportDate,
					orderField))
			}
		} else {
			// 如果排序字段不合法，使用默认排序
			mod = mod.Order(fmt.Sprintf("%s DESC, %s DESC",
				dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().ReportDate,
				dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().Id))
		}
	} else {
		// 默认排序
		mod = mod.Order(fmt.Sprintf("%s DESC, %s DESC",
			dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().ReportDate,
			dao.UsercenterUserReport.Table()+"."+dao.UsercenterUserReport.Columns().Id))
	}

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取用户日报列表失败，请稍后重试！")
		return
	}

	// 使用单个查询获取统计数据
	var stats struct {
		DsNum          int64   `json:"ds_num"`
		DsSuccess      int64   `json:"ds_success"`
		DsMoney        float64 `json:"ds_money"`
		DsSuccessMoney float64 `json:"ds_success_money"`
		DsFee          float64 `json:"ds_fee"`
		DfNum          int64   `json:"df_num"`
		DfSuccess      int64   `json:"df_success"`
		DfMoney        float64 `json:"df_money"`
		DfSuccessMoney float64 `json:"df_success_money"`
		DfFee          float64 `json:"df_fee"`
		DfDcl          float64 `json:"df_dcl"`
		DfYcl          float64 `json:"df_ycl"`
		DfDclNum       int64   `json:"df_dcl_num"`
	}

	statsMod := s.Model(ctx)
	// 添加报表日期筛选
	if in.ReportDate != nil {
		statsMod = statsMod.Where(dao.UsercenterUserReport.Columns().ReportDate, in.ReportDate.Format("Y-m-d"))
	} else {
		statsMod = statsMod.Where(dao.UsercenterUserReport.Columns().ReportDate, gtime.Now().Format("Y-m-d"))
	}

	// 查询id
	if len(in.Ids) > 0 {
		statsMod = statsMod.WhereIn(dao.UsercenterUserReport.Columns().Id, in.Ids)
	}

	err = statsMod.Fields(
		"SUM(hg_usercenter_user_report.ds_num) as dsNum",
		"SUM(hg_usercenter_user_report.ds_success) as dsSuccess",
		"SUM(hg_usercenter_user_report.ds_money) as dsMoney",
		"SUM(hg_usercenter_user_report.ds_success_money) as dsSuccessMoney",
		"SUM(hg_usercenter_user_report.ds_fee) as dsFee",
		"SUM(hg_usercenter_user_report.df_num) as dfNum",
		"SUM(hg_usercenter_user_report.df_success) as dfSuccess",
		"SUM(hg_usercenter_user_report.df_money) as dfMoney",
		"SUM(hg_usercenter_user_report.df_success_money) as dfSuccessMoney",
		"SUM(hg_usercenter_user_report.df_fee) as dfFee",
		"SUM(hg_usercenter_user_report.df_dcl) as dfDcl",
		"SUM(hg_usercenter_user_report.df_ycl) as dfYcl",
		"SUM(hg_usercenter_user_report.df_dcl_num) as dfDclNum",
	).Scan(&stats)
	if err != nil {
		err = gerror.Wrap(err, "获取统计数据失败，请稍后重试！")
		return
	}

	// 计算成功率
	dsCgl := 0
	if stats.DsNum > 0 {
		dsCgl = int(float64(stats.DsSuccess) / float64(stats.DsNum) * 100)
	}
	dfCgl := 0
	if stats.DfNum > 0 {
		dfCgl = int(float64(stats.DfSuccess) / float64(stats.DfNum) * 100)
	}

	odRes = &usercenteruserreport.OdRes{
		DsNum:          stats.DsNum,
		DsSuccess:      stats.DsSuccess,
		DsCgl:          dsCgl,
		DsMoney:        stats.DsMoney,
		DsSuccessMoney: stats.DsSuccessMoney,
		DfNum:          stats.DfNum,
		DfSuccess:      stats.DfSuccess,
		DfCgl:          dfCgl,
		DfMoney:        stats.DfMoney,
		DfSuccessMoney: stats.DfSuccessMoney,
	}
	return
}

// Export 导出用户日报
func (s *sSysUsercenterUserReport) Export(ctx context.Context, in *sysin.UsercenterUserReportListInp) (err error) {
	var list []*sysin.UsercenterUserReportListModel // 声明list变量

	// 先获取符合条件的总数量，用于判断是否需要大数据导出
	// 设置为第一页，每页1条，只为获取总数量
	in.Page = 1
	in.PerPage = 1
	_, totalCount, _, err := s.List(ctx, in)
	if err != nil {
		return
	}
	if totalCount == 0 {
		err = gerror.New("没有数据")
		return
	}
	// 大数据导出判断：当总记录数大于1万条时，走异步大数据导出
	if totalCount > 10000 {
		return s.ExportBigData(ctx, in)
	}

	// 小数据量走原有的同步导出逻辑
	// 查询所有符合条件的数据（不分页）
	in.Page = 1
	// in.PerPage = int(totalCount) // 设置为总数量，确保获取所有数据
	in.PerPage = int(totalCount)
	list, _, _, err = s.List(ctx, in)
	if err != nil {
		return
	}
	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.UsercenterUserReportExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出用户日报-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.UsercenterUserReportExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// UserExport 导出用户日报
func (s *sSysUsercenterUserReport) UserExport(ctx context.Context, in *sysin.UsercenterUserReportListInp) (err error) {
	var list []*sysin.UsercenterUserReportListModel // 声明list变量

	// 先获取符合条件的总数量，用于判断是否需要大数据导出
	// 设置为第一页，每页1条，只为获取总数量
	in.Page = 1
	in.PerPage = 1
	_, totalCount, _, err := s.List(ctx, in)
	if err != nil {
		return
	}
	if totalCount == 0 {
		err = gerror.New("没有数据")
		return
	}
	// 大数据导出判断：当总记录数大于1万条时，走异步大数据导出
	if totalCount > 10000 {
		return s.UserExportBigData(ctx, in)
	}

	// 小数据量走原有的同步导出逻辑
	// 查询所有符合条件的数据（不分页）
	in.Page = 1
	// in.PerPage = int(totalCount) // 设置为总数量，确保获取所有数据
	in.PerPage = int(totalCount)
	list, _, _, err = s.List(ctx, in)
	if err != nil {
		return
	}
	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.UsercenterUserReportUserExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出用户日报-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.UsercenterUserReportUserExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增用户日报
func (s *sSysUsercenterUserReport) Edit(ctx context.Context, in *sysin.UsercenterUserReportEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			if _, err = s.Model(ctx).
				Fields(sysin.UsercenterUserReportUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改用户日报失败，请稍后重试！")
			}
			return
		}

		// 新增
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.UsercenterUserReportInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增用户日报失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除用户日报
func (s *sSysUsercenterUserReport) Delete(ctx context.Context, in *sysin.UsercenterUserReportDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Delete(); err != nil {
		err = gerror.Wrap(err, "删除用户日报失败，请稍后重试！")
		return
	}
	return
}

// View 获取用户日报指定信息
func (s *sSysUsercenterUserReport) View(ctx context.Context, in *sysin.UsercenterUserReportViewInp) (res *sysin.UsercenterUserReportViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取用户日报信息，请稍后重试！")
		return
	}
	return
}

// 真正的导出实现
func (s *sSysUsercenterUserReport) processUserExportTask(ctx context.Context, taskId int64, params string) error {
	// ==================== 类型配置区 - 复用时只需修改这部分 ====================
	// 类型配置 - 复用时需要修改的类型
	type (
		ListInputType    = sysin.UsercenterUserReportListInp         // 列表查询输入类型
		ListOutputType   = sysin.UsercenterUserReportListModel       // 列表输出类型
		ExportOutputType = sysin.UsercenterUserReportUserExportModel // 导出输出类型
	)

	// 服务方法配置 - 复用时需要修改的方法调用
	listFunc := func(ctx context.Context, in *ListInputType) ([]*ListOutputType, int, error) {
		list, totalCount, _, err := s.List(ctx, in)
		return list, totalCount, err
	}
	// ==================== 配置区结束 ====================

	g.Log().Infof(ctx, "========== 开始处理大数据导出任务 %d ==========", taskId)

	// 1. 解析参数
	var in ListInputType
	if err := gjson.Unmarshal([]byte(params), &in); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 解析导出参数失败: %v", taskId, err)
		return gerror.Wrap(err, "解析导出参数失败")
	}
	g.Log().Infof(ctx, "任务 %d: 参数解析成功: %+v", taskId, in)

	// 2. 获取总记录数（用于计算分批次数和进度）
	// 首先更新进度为1% - 开始查询总记录数
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 1, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新初始进度失败: %v", taskId, err)
	}
	g.Log().Infof(ctx, "任务 %d: 开始查询总记录数...", taskId)

	in.Page = 1
	in.PerPage = 1
	_, totalCount, err := listFunc(ctx, &in)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 查询总记录数失败: %v", taskId, err)
		return gerror.Wrap(err, "查询总记录数失败")
	}
	g.Log().Infof(ctx, "任务 %d: 查询到总记录数: %d 条", taskId, totalCount)

	// 总记录数查询完成，更新进度为3%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 3, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新总记录数查询进度失败: %v", taskId, err)
	}

	if totalCount == 0 {
		g.Log().Warningf(ctx, "任务 %d: 没有数据需要导出，直接完成任务", taskId)
		return common.NewCommonExport().CompleteTask(ctx, taskId, []string{}, "", 0, 0)
	}

	// 每批处理5000条记录，生成一个Excel文件
	totalPages := (totalCount + s.BatchSize - 1) / s.BatchSize // 计算总批次

	g.Log().Infof(ctx, "任务 %d: 分批策略 - 总记录数=%d, 每批大小=%d, 总文件数=%d",
		taskId, totalCount, s.BatchSize, totalPages)

	// 4. 准备文件存储目录
	// 更新进度为5% - 开始准备导出环境
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 5, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新环境准备进度失败: %v", taskId, err)
		return gerror.Wrap(err, "获取上传配置失败")
	}
	g.Log().Infof(ctx, "任务 %d: 开始准备导出环境...", taskId)

	serverRoot := g.Cfg().MustGet(ctx, "server.serverRoot", "resource/public").String()
	uploadConfig, err := isc.SysConfig().GetUpload(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 获取上传配置失败: %v", taskId, err)
		return gerror.Wrap(err, "获取上传配置失败")
	}

	exportDir := serverRoot + "/" + strings.Trim(uploadConfig.LocalPath, "/") + "/export/" + fmt.Sprintf("task_%d/", taskId)
	if !gfile.Exists(exportDir) {
		if err = gfile.Mkdir(exportDir); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 创建导出目录失败: %v, 目录: %s", taskId, err, exportDir)
			return gerror.Wrap(err, "创建导出目录失败")
		}
	}
	g.Log().Infof(ctx, "任务 %d: 导出目录创建成功: %s", taskId, exportDir)

	// 环境准备完成，更新进度为8%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 8, 0); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新环境准备完成进度失败: %v", taskId, err)
	}

	var excelFiles []string // 收集所有生成的Excel文件路径
	var processedRecords int64 = 0

	// 5. 分批处理数据，每批生成一个Excel文件
	for page := 1; page <= totalPages; page++ {
		g.Log().Infof(ctx, "任务 %d: ========== 开始处理第 %d/%d 批 ==========", taskId, page, totalPages)

		// 更新进度 - 开始处理当前批次（8% + 85% * 当前批次进度）
		batchStartProgress := 8 + int(float64(page-1)/float64(totalPages)*85)
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchStartProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批开始进度失败: %v", taskId, page, err)
		}

		// 设置分页参数
		in.Page = page
		in.PerPage = s.BatchSize

		// 查询当前批次数据
		g.Log().Infof(ctx, "任务 %d: 查询第 %d 批数据，页码=%d，每页=%d", taskId, page, in.Page, in.PerPage)
		list, currentCount, err := listFunc(ctx, &in)
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 查询第%d批数据失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("查询第%d批数据失败", page))
		}
		g.Log().Infof(ctx, "任务 %d: 第 %d 批查询成功，获得 %d 条记录", taskId, page, currentCount)

		// 数据查询完成，更新批次内细粒度进度（+1%）
		batchDataProgress := batchStartProgress + 1
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchDataProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据查询进度失败: %v", taskId, page, err)
		}

		// 转换为导出结构体
		var exports []ExportOutputType
		if err := gconv.Scan(list, &exports); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批数据结构转换失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批数据结构转换失败", page))
		}
		g.Log().Infof(ctx, "任务 %d: 第 %d 批数据转换成功，准备生成Excel文件", taskId, page)

		// 6. 为当前批次创建独立的Excel文件
		f := excelize.NewFile()
		sheetName := fmt.Sprintf("%s_%d", s.SheetNamePrefix, page)
		f.SetSheetName("Sheet1", sheetName)

		// 设置表头
		tags, err := convert.GetEntityDescTags(ExportOutputType{})
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批获取导出字段失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批获取导出字段失败", page))
		}

		// 7. 写入当前批次的数据 - 优化为真正的批量写入
		g.Log().Infof(ctx, "任务 %d: 开始准备第 %d 批数据进行批量写入", taskId, page)

		// Excel文件准备完成，更新批次内细粒度进度（+1%）
		batchExcelPrepProgress := batchDataProgress + 1
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchExcelPrepProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批Excel准备进度失败: %v", taskId, page, err)
		}

		// 预先构造所有行数据
		allRows := make([][]interface{}, len(exports))
		for i, export := range exports {
			// 使用反射获取结构体字段值
			t := reflect.TypeOf(export)
			value := reflect.ValueOf(export)
			row := make([]interface{}, 0, t.NumField()) // 预分配容量

			for j := 0; j < t.NumField(); j++ {
				val := value.Field(j).Interface()
				row = append(row, val)
			}

			allRows[i] = row
			processedRecords++

			// 每处理1000条记录打印一次进度并更新数据库进度
			if (i+1)%1000 == 0 {
				g.Log().Infof(ctx, "任务 %d: 第 %d 批已准备 %d/%d 条记录", taskId, page, i+1, len(exports))
				// 数据准备过程中的细粒度进度更新
				dataProgress := batchExcelPrepProgress + int(float64(i+1)/float64(len(exports))*2) // 数据准备占2%
				if err := common.NewCommonExport().UpdateProgress(ctx, taskId, dataProgress, processedRecords); err != nil {
					g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据准备进度失败: %v", taskId, page, err)
				}
			}
		}

		// 数据准备完成，更新进度
		batchDataPreparedProgress := batchExcelPrepProgress + 2
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchDataPreparedProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批数据准备完成进度失败: %v", taskId, page, err)
		}

		// 使用高效的批量写入方法
		g.Log().Infof(ctx, "任务 %d: 开始使用StreamWriter批量写入表头和 %d 条数据", taskId, len(allRows))

		// 创建流式写入器以提高大批量数据写入性能
		streamWriter, err := f.NewStreamWriter(sheetName)
		if err != nil {
			g.Log().Errorf(ctx, "任务 %d: 创建StreamWriter失败: %v", taskId, err)
			return gerror.Wrap(err, "创建StreamWriter失败")
		}

		// 首先写入表头到StreamWriter
		headerRow := make([]interface{}, len(tags))
		for i, tag := range tags {
			headerRow[i] = tag
		}
		if err := streamWriter.SetRow("A1", headerRow); err != nil {
			streamWriter.Flush()
			g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入表头失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入表头失败", page))
		}

		// 设置表头样式（在StreamWriter之外设置样式）
		headerStyle, _ := f.NewStyle(`{
			"font":{"bold":true,"color":"#000000","size":12},
			"fill":{"type":"pattern","color":["#E0E0E0"],"pattern":1},
			"alignment":{"horizontal":"center","vertical":"center"},
			"border":[{"type":"left","color":"#000000","style":1},{"type":"top","color":"#000000","style":1},{"type":"bottom","color":"#000000","style":1},{"type":"right","color":"#000000","style":1}]
		}`)

		// 使用流式写入批量写入所有数据
		for i, row := range allRows {
			rowNum := i + 2 // 从第2行开始（第1行是表头）
			cellRange := fmt.Sprintf("A%d", rowNum)
			if err := streamWriter.SetRow(cellRange, row); err != nil {
				streamWriter.Flush()
				g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入第%d行数据失败: %v", taskId, page, rowNum, err)
				return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入第%d行数据失败", page, rowNum))
			}

			// 每写入1000行更新一次进度
			if (i+1)%1000 == 0 {
				writeProgress := batchDataPreparedProgress + int(float64(i+1)/float64(len(allRows))*2) // Excel写入占2%
				if err := common.NewCommonExport().UpdateProgress(ctx, taskId, writeProgress, processedRecords); err != nil {
					g.Log().Errorf(ctx, "任务 %d: 更新第%d批Excel写入进度失败: %v", taskId, page, err)
				}
			}
		}

		// 完成流式写入
		if err := streamWriter.Flush(); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 第%d批流式写入flush失败: %v", taskId, page, err)
			return gerror.Wrap(err, fmt.Sprintf("第%d批流式写入flush失败", page))
		}

		// 在StreamWriter完成后设置表头样式
		headerRange := fmt.Sprintf("A1:%s1", string(rune('A'+len(tags)-1)))
		if err := f.SetCellStyle(sheetName, "A1", headerRange, headerStyle); err != nil {
			g.Log().Warningf(ctx, "任务 %d: 第%d批设置表头样式失败: %v", taskId, page, err)
			// 样式设置失败不影响数据导出，只记录警告
		}

		g.Log().Infof(ctx, "任务 %d: 第 %d 批数据流式写入完成，共写入表头+%d条记录", taskId, page, len(allRows))

		// Excel写入完成，更新进度
		batchWriteCompleteProgress := batchDataPreparedProgress + 2
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, batchWriteCompleteProgress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新第%d批写入完成进度失败: %v", taskId, page, err)
		}

		// 8. 保存当前批次的Excel文件
		fileName := fmt.Sprintf(s.ExportName+"_第%d批_共%d条.xlsx", page, len(exports))
		filePath := exportDir + fileName

		if err := f.SaveAs(filePath); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 保存第%d批Excel文件失败: %v, 路径: %s", taskId, page, err, filePath)
			return gerror.Wrap(err, fmt.Sprintf("保存第%d批Excel文件失败", page))
		}

		excelFiles = append(excelFiles, filePath)
		g.Log().Infof(ctx, "任务 %d: 第 %d 批Excel文件生成成功: %s，包含 %d 条记录",
			taskId, page, fileName, len(exports))

		// 9. 更新任务进度 - 当前批次完全处理完成
		progress := 8 + int(float64(page)/float64(totalPages)*85) // 8% 初始化 + 85% 数据处理
		if err := common.NewCommonExport().UpdateProgress(ctx, taskId, progress, processedRecords); err != nil {
			g.Log().Errorf(ctx, "任务 %d: 更新进度失败: %v", taskId, err)
		}

		g.Log().Infof(ctx, "任务 %d: 第 %d 批处理完成，已处理 %d/%d 条记录，进度 %d%%",
			taskId, page, processedRecords, totalCount, progress)

		// 适当休眠，避免数据库和磁盘压力
		if page < totalPages {
			time.Sleep(200 * time.Millisecond)
		}
	}

	g.Log().Infof(ctx, "任务 %d: ========== 所有Excel文件生成完成，开始ZIP打包 ==========", taskId)
	g.Log().Infof(ctx, "任务 %d: 共生成 %d 个Excel文件，总记录数 %d 条", taskId, len(excelFiles), processedRecords)

	// 10. 打包所有Excel文件为ZIP
	zipFileName := fmt.Sprintf(s.ExportName+"_任务%d_共%d条记录.zip", taskId, processedRecords)
	zipFilePath := exportDir + zipFileName

	err = file.CreateZipFromFiles(excelFiles, zipFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: ZIP打包失败: %v", taskId, err)
		return gerror.Wrap(err, "ZIP打包失败")
	}

	// 获取ZIP文件大小
	zipFileInfo, err := gfile.Stat(zipFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "任务 %d: 获取ZIP文件信息失败: %v", taskId, err)
		return gerror.Wrap(err, "获取ZIP文件信息失败")
	}

	g.Log().Infof(ctx, "任务 %d: ZIP文件打包成功: %s，文件大小: %.2fMB",
		taskId, zipFileName, float64(zipFileInfo.Size())/1024/1024)

	// 12. 清理原始Excel文件，节省磁盘空间
	g.Log().Infof(ctx, "任务 %d: 开始清理原始Excel文件...", taskId)
	deletedCount := 0
	for _, excelFile := range excelFiles {
		if err := gfile.Remove(excelFile); err != nil {
			g.Log().Warningf(ctx, "任务 %d: 删除Excel文件失败: %s, error: %v", taskId, excelFile, err)
		} else {
			deletedCount++
			g.Log().Debugf(ctx, "任务 %d: 已删除Excel文件: %s", taskId, filepath.Base(excelFile))
		}
	}
	g.Log().Infof(ctx, "任务 %d: Excel文件清理完成，成功删除 %d/%d 个文件",
		taskId, deletedCount, len(excelFiles))

	// 更新进度到100%
	if err := common.NewCommonExport().UpdateProgress(ctx, taskId, 100, processedRecords); err != nil {
		g.Log().Errorf(ctx, "任务 %d: 更新最终进度失败: %v", taskId, err)
	}

	// 11. 完成任务，传递ZIP文件路径和下载URL - 修复URL构建逻辑
	// 构建相对于静态根目录的路径
	relativePath := strings.Trim(uploadConfig.LocalPath, "/") + "/export/" + fmt.Sprintf("task_%d/", taskId) + zipFileName
	downloadUrl := "/" + relativePath

	g.Log().Infof(ctx, "任务 %d: ========== 导出任务完成 ==========", taskId)
	g.Log().Infof(ctx, "任务 %d: 文件信息 - ZIP文件:%s, 文件大小:%.2fMB",
		taskId, zipFileName, float64(zipFileInfo.Size())/1024/1024)
	g.Log().Infof(ctx, "任务 %d: 路径信息 - 物理路径:%s, 下载地址:%s",
		taskId, zipFilePath, downloadUrl)
	g.Log().Infof(ctx, "任务 %d: 统计信息 - 总记录数:%d, Excel文件数:%d",
		taskId, processedRecords, len(excelFiles))

	return common.NewCommonExport().CompleteTask(ctx, taskId, []string{zipFilePath}, downloadUrl, len(excelFiles), zipFileInfo.Size())
}
