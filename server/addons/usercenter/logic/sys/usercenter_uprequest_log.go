// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.7
package sys

import (
	"context"
	"fmt"
	"hotgo/addons/usercenter/model/input/sysin"
	"hotgo/addons/usercenter/service"
	"hotgo/internal/dao"
	"hotgo/internal/library/hgorm"
	"hotgo/internal/library/hgorm/handler"
	"hotgo/internal/model/input/form"
	isc "hotgo/internal/service"
	"hotgo/utility/convert"
	"hotgo/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysUsercenterUprequestLog struct{}

func NewSysUsercenterUprequestLog() *sSysUsercenterUprequestLog {
	return &sSysUsercenterUprequestLog{}
}

func init() {
	service.RegisterSysUsercenterUprequestLog(NewSysUsercenterUprequestLog())
	// 构造一个字典项目
	// dict.RegisterFunc("payment_type", "支付类型", PaymentType)
}

// Model 请求上游日志ORM模型
func (s *sSysUsercenterUprequestLog) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.UsercenterUprequestLog.Ctx(ctx), option...)
}

// List 获取请求上游日志列表
func (s *sSysUsercenterUprequestLog) List(ctx context.Context, in *sysin.UsercenterUprequestLogListInp) (list []*sysin.UsercenterUprequestLogListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.FieldsPrefix(dao.UsercenterUprequestLog.Table(), sysin.UsercenterUprequestLogListModel{})

	// 过滤关联表字段
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.UsercenterUprequestLogListModel{}, &dao.UsercenterPayConfig, "qudao"))
	// 关联表字段
	mod = mod.LeftJoinOnFields(dao.UsercenterPayConfig.Table(), dao.UsercenterUprequestLog.Columns().PaytmId, "=", dao.UsercenterPayConfig.Columns().Id)
	dfsearch := false
	// 查询商户订单id
	if in.Tid != "" {
		mod = mod.WhereLike(dao.UsercenterUprequestLog.Columns().Tid, in.Tid)
		dfsearch = true
	}

	// 查询系统订单id
	if in.Oid != "" {
		mod = mod.WhereLike(dao.UsercenterUprequestLog.Columns().Oid, in.Oid)
		dfsearch = true
	}

	// 查询状态
	if in.Status > 0 {
		mod = mod.Where(dao.UsercenterUprequestLog.Columns().Status, in.Status)
		dfsearch = true
	}

	// 查询类型
	if in.Type > 0 {
		mod = mod.Where(dao.UsercenterUprequestLog.Columns().Type, in.Type-1)
		dfsearch = true
	}

	// 查询调用行
	if in.Line != "" {
		mod = mod.WhereLike(dao.UsercenterUprequestLog.Columns().Line, "%"+in.Line+"%")
		dfsearch = true
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.UsercenterUprequestLog.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
		dfsearch = true
	}

	// 查询渠道名称
	if in.Qudaoname != "" {
		mod = mod.WherePrefixLike(dao.UsercenterPayConfig.Table(), dao.UsercenterPayConfig.Columns().Name, in.Qudaoname)
		dfsearch = true
	}
	if !dfsearch {
		// 获取通用配置中的数据模式
		commonConfig, err := isc.SysConfig().GetCommon(ctx)
		if err == nil && commonConfig != nil {
			// 如果是节能模式(dataMode=1)，默认显示今天的数据
			if commonConfig.DataMode == 1 {
				// 获取今天的起始和结束时间
				todayStart := gtime.Now().StartOfDay()
				todayEnd := gtime.Now().EndOfDay()
				fmt.Println("todayStart", todayStart)
				fmt.Println("todayEnd", todayEnd)
				mod = mod.WhereBetween(dao.UsercenterUprequestLog.Columns().CreatedAt, todayStart, todayEnd)
			}
			// 如果是全量模式(dataMode=2)，不添加时间筛选，显示所有数据
		}
	}
	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.UsercenterUprequestLog.Columns().Id)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取请求上游日志列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出请求上游日志
func (s *sSysUsercenterUprequestLog) Export(ctx context.Context, in *sysin.UsercenterUprequestLogListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.UsercenterUprequestLogExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出请求上游日志-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.UsercenterUprequestLogExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增请求上游日志
func (s *sSysUsercenterUprequestLog) Edit(ctx context.Context, in *sysin.UsercenterUprequestLogEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			if _, err = s.Model(ctx).
				Fields(sysin.UsercenterUprequestLogUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改请求上游日志失败，请稍后重试！")
			}
			return
		}

		// 新增
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.UsercenterUprequestLogInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增请求上游日志失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除请求上游日志
func (s *sSysUsercenterUprequestLog) Delete(ctx context.Context, in *sysin.UsercenterUprequestLogDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Delete(); err != nil {
		err = gerror.Wrap(err, "删除请求上游日志失败，请稍后重试！")
		return
	}
	return
}

// View 获取请求上游日志指定信息
func (s *sSysUsercenterUprequestLog) View(ctx context.Context, in *sysin.UsercenterUprequestLogViewInp) (res *sysin.UsercenterUprequestLogViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取请求上游日志信息，请稍后重试！")
		return
	}
	return
}

// Status 更新请求上游日志状态
func (s *sSysUsercenterUprequestLog) Status(ctx context.Context, in *sysin.UsercenterUprequestLogStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.UsercenterUprequestLog.Columns().Status: in.Status,
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新请求上游日志状态失败，请稍后重试！")
		return
	}
	return
}
