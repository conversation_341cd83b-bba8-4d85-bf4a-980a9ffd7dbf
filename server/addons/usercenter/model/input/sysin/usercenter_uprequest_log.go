// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.7
package sysin

import (
	"context"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UsercenterUprequestLogUpdateFields 修改请求上游日志字段过滤
type UsercenterUprequestLogUpdateFields struct {
	Tid            string      `json:"tid"            dc:"商户订单id"`
	Oid            string      `json:"oid"            dc:"系统订单id"`
	LevelFormat    string      `json:"levelFormat"    dc:"日志级别"`
	Content        string      `json:"content"        dc:"请求内容"`
	RequestContent string      `json:"requestContent" dc:"响应内容"`
	Stack          *gjson.Json `json:"stack"          dc:"打印堆栈"`
	Type           int         `json:"type"           dc:"类型"`
	Line           string      `json:"line"           dc:"调用行"`
	TriggerNs      int64       `json:"triggerNs"      dc:"请求耗时(秒)"`
	Status         int         `json:"status"         dc:"状态"`
}

// UsercenterUprequestLogInsertFields 新增请求上游日志字段过滤
type UsercenterUprequestLogInsertFields struct {
	Tid            string      `json:"tid"            dc:"商户订单id"`
	Oid            string      `json:"oid"            dc:"系统订单id"`
	LevelFormat    string      `json:"levelFormat"    dc:"日志级别"`
	Content        string      `json:"content"        dc:"请求内容"`
	RequestContent string      `json:"requestContent" dc:"响应内容"`
	Stack          *gjson.Json `json:"stack"          dc:"打印堆栈"`
	Type           int         `json:"type"           dc:"类型"`
	Line           string      `json:"line"           dc:"调用行"`
	TriggerNs      int64       `json:"triggerNs"      dc:"请求耗时(秒)"`
	Status         int         `json:"status"         dc:"状态"`
}

// UsercenterUprequestLogEditInp 修改/新增请求上游日志
type UsercenterUprequestLogEditInp struct {
	entity.UsercenterUprequestLog
}

func (in *UsercenterUprequestLogEditInp) Filter(ctx context.Context) (err error) {
	// 验证商户订单id
	if err := g.Validator().Rules("required").Data(in.Tid).Messages("商户订单id不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证系统订单id
	if err := g.Validator().Rules("required").Data(in.Oid).Messages("系统订单id不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证类型
	if err := g.Validator().Rules("required").Data(in.Type).Messages("类型不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2").Data(in.Type).Messages("类型值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证调用行
	if err := g.Validator().Rules("required").Data(in.Line).Messages("调用行不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证状态
	if err := g.Validator().Rules("required").Data(in.Status).Messages("状态不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2").Data(in.Status).Messages("状态值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type UsercenterUprequestLogEditModel struct{}

// UsercenterUprequestLogDeleteInp 删除请求上游日志
type UsercenterUprequestLogDeleteInp struct {
	Id interface{} `json:"id" v:"required#日志ID不能为空" dc:"日志ID"`
}

func (in *UsercenterUprequestLogDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type UsercenterUprequestLogDeleteModel struct{}

// UsercenterUprequestLogViewInp 获取指定请求上游日志信息
type UsercenterUprequestLogViewInp struct {
	Id int64 `json:"id" v:"required#日志ID不能为空" dc:"日志ID"`
}

func (in *UsercenterUprequestLogViewInp) Filter(ctx context.Context) (err error) {
	return
}

type UsercenterUprequestLogViewModel struct {
	entity.UsercenterUprequestLog
}

// UsercenterUprequestLogListInp 获取请求上游日志列表
type UsercenterUprequestLogListInp struct {
	form.PageReq
	Tid       string        `json:"tid"       dc:"商户订单id"`
	Oid       string        `json:"oid"       dc:"系统订单id"`
	Status    int           `json:"status"    dc:"状态"`
	Type      int           `json:"type"      dc:"类型"`
	Line      string        `json:"line"      dc:"调用行"`
	CreatedAt []*gtime.Time `json:"createdAt" dc:"创建时间"`
	Qudaoname string        `json:"qudaoname" dc:"渠道名称"`
}

func (in *UsercenterUprequestLogListInp) Filter(ctx context.Context) (err error) {
	return
}

type UsercenterUprequestLogListModel struct {
	Id             int64       `json:"id"          dc:"日志ID"`
	Tid            string      `json:"tid"         dc:"商户订单id"`
	Oid            string      `json:"oid"         dc:"系统订单id"`
	LevelFormat    string      `json:"levelFormat" dc:"日志级别"`
	Type           int         `json:"type"        dc:"类型"`
	Line           string      `json:"line"        dc:"调用行"`
	TriggerNs      int64       `json:"triggerNs"   dc:"请求耗时(秒)"`
	Status         int         `json:"status"      dc:"状态"`
	Stack          *gjson.Json `json:"stack"       dc:"打印堆栈"`
	CreatedAt      *gtime.Time `json:"createdAt"   dc:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updatedAt"   dc:"修改时间"`
	Content        *gjson.Json `json:"content"   dc:"请求内容"`
	RequestContent *gjson.Json `json:"requestContent"  dc:"响应内容"`
	QudaoName      string      `json:"qudaoname"  dc:"渠道名"`
}

// UsercenterUprequestLogExportModel 导出请求上游日志
type UsercenterUprequestLogExportModel struct {
	Id          int64       `json:"id"          dc:"日志ID"`
	Tid         string      `json:"tid"         dc:"商户订单id"`
	Oid         string      `json:"oid"         dc:"系统订单id"`
	LevelFormat string      `json:"levelFormat" dc:"日志级别"`
	Type        int         `json:"type"        dc:"类型"`
	Line        string      `json:"line"        dc:"调用行"`
	TriggerNs   int64       `json:"triggerNs"   dc:"请求耗时(秒)"`
	Status      int         `json:"status"      dc:"状态"`
	CreatedAt   *gtime.Time `json:"createdAt"   dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   dc:"修改时间"`
}

// UsercenterUprequestLogStatusInp 更新请求上游日志状态
type UsercenterUprequestLogStatusInp struct {
	Id     int64 `json:"id" v:"required#日志ID不能为空" dc:"日志ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *UsercenterUprequestLogStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("日志ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type UsercenterUprequestLogStatusModel struct{}
