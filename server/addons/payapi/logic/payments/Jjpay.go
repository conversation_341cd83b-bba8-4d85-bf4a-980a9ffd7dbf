package payments

import (
	"context"
	"encoding/json"
	"fmt"
	"hotgo/addons/payapi/consts"
	"hotgo/addons/payapi/model/input/payapi"
	"hotgo/addons/payapi/service"
	uonst "hotgo/addons/usercenter/consts"
	"hotgo/addons/usercenter/library/common"
	userver "hotgo/addons/usercenter/service"
	"hotgo/internal/dao"
	"hotgo/internal/library/location"
	"hotgo/internal/model/entity"
	isc "hotgo/internal/service"
	"net/http"
	"sort"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// Dspay 支付模块
type Jjpay struct{}

func (d *Jjpay) GetBaseUrl() string {
	// return "http://mcapi.u9mcapi.com"
	return "http://api.aohslia.com"
}

func (d *Jjpay) CreateOrder(ctx context.Context, req *payapi.CreateOrderInp, mb *entity.UsercenterUser, qudao gdb.Record, feedata payapi.QudaoList) (res *payapi.CreateOrderOup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// 示例数据

	tradeNo := common.GenerateOrderID("CPDS")

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{
		"ChannelCode": strings.TrimSpace(PayConfig["order_passage_code"]),
		"McorderNo":   tradeNo,

		"Type":   "INR",
		"Amount": req.Money,
	}
	domain := "https://" + g.Cfg().MustGet(ctx, "payapi.apidomain").String()
	// 假设这是当前的域名获取
	callbackURL := domain + "/api/payapi/v1/dsnotify/Jjpay"

	// 添加 notifyurl 和 types
	sfdata["CallBackUrl"] = callbackURL
	sfdata["JumpUrl"] = "https://www.google.com"
	// 对 sfdata 按键名进行排序
	keys := make([]string, 0, len(sfdata))
	for k := range sfdata {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/create", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/create", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道响应错误", tradeNo, req.Tid, 0, 0, uonst.ERRO, err, requestData, "", 0, qudao["id"].Int64())
		return
	}
	// 读取响应内容
	content := response.ReadAllString()

	defer response.Close()
	endTime := gtime.Now()
	duration := endTime.Sub(startTime)
	seconds := int64(duration.Seconds()) // 获取时间差的秒数
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		service.PayApi().LogRequest(ctx, "通道响应错误", tradeNo, req.Tid, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		service.PayApi().LogRequest(ctx, "上游响应json格式错误", tradeNo, req.Tid, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return

	}
	code := result["code"].(float64)
	// code, ok := result["code"].(int64)
	// if ok {
	// 	if code==0{
	// 		err = gerror.New("响应数据类型错误")
	// 	return
	// 	}
	// }else{
	// 	err = gerror.New("响应数据类型错误")
	// 	return
	// }
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		service.PayApi().LogRequest(ctx, msg, tradeNo, req.Tid, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}
	// var payurl, tradeNo string
	var payurl, ThirdOrderno string
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 获取 payurl
		payurl = data["payUrl"].(string)

		// 获取 TradeNo
		ThirdOrderno = data["orderNo"].(string)

	} else {
		err = gerror.New("data 字段不存在或解析失败")
		service.PayApi().LogRequest(ctx, "data 字段不存在或解析失败", tradeNo, req.Tid, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}
	// 输出最终的 sfdata

	// 先来更新比树和成功率
	ds_cgl := qudao["ds_cgl"].Int()
	ds_hatnum := qudao["ds_hatnum"].Int() + 1
	if qudao["ds_success"].Int() != 0 {
		ds_cgl = int(qudao["ds_success"].Float64() / float64(ds_hatnum) * 100)
	}
	payconfgdata := g.Map{
		"ds_hatnum": ds_hatnum,
		"ds_cgl":    ds_cgl,
	}

	// 使用事务处理订单创建逻辑
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		var releorder gdb.Record // 声明变量
		releorder, err = tx.Model("usercenter_order").Where("user_orderno", req.Tid).WhereOr("sys_orderno", req.Tid).
			One()
		if err != nil {
			return gerror.Wrap(err, "查询订单号失败")
		}
		// 如果查询到的订单不为空，说明订单号重复
		if releorder != nil {
			return gerror.New("订单号已存在")
		}
		// 将 Money 从 string 转换为 float64
		moneyFloat, err := strconv.ParseFloat(req.Money, 64)
		if err != nil {
			return gerror.New("金额格式不正确")
		}
		// 检查ip允许情况
		r := ghttp.RequestFromCtx(ctx)
		// 获取ip处理
		clientIp := location.GetClientIp(r)
		// 构造订单数据
		Fee := moneyFloat * mb.DsFee

		// 计算代理费率差
		dsfee_cha := float64(0)
		if mb.Pid != 0 {
			// 获取完整代理链
			upUsers, err := GetTopArr(ctx, mb.Pid, mb.DsFee, mb.Fl, 1, moneyFloat)
			fmt.Printf("上级链:%s\n", gjson.MustEncode(upUsers))
			if err != nil {
				return gerror.Wrap(err, "获取代理链失败")
			}

			// 找到最顶级代理的费率
			var topDsFee float64
			if len(upUsers) > 0 {
				topDsFee = upUsers[len(upUsers)-1].DsFee
			}

			// 计算费率差 = 自己的费率 - 最顶级代理的费率
			dsfee_cha = mb.DsFee - topDsFee
			fmt.Printf("最顶层费率差:%f\n", dsfee_cha)
		}

		dlfee := moneyFloat * dsfee_cha
		orderData := &entity.UsercenterOrder{
			UserOrderno:  req.Tid,
			SysOrderno:   tradeNo,
			ThirdOrderno: ThirdOrderno,
			UserId:       int64(mb.Id),
			Money:        moneyFloat,
			Status:       0,
			Fee:          Fee,
			CallbackUrl:  req.CallbackUrl,
			Ip:           clientIp,
			DlFee:        dlfee,
			PaytmId:      qudao["id"].Int64(),
			Payurl:       payurl,
			IsRequest:    1,
		}
		// 插入订单
		id, err := dao.UsercenterOrder.Ctx(ctx).TX(tx).OmitEmpty().Data(orderData).InsertAndGetId()
		if err != nil {
			return gerror.Wrap(err, "订单插入失败")
		}

		// 插入订单日志
		orderlog := &entity.UsercenterOrderLog{
			Orderno:   orderData.SysOrderno,
			CreatedBy: 0,
			Event:     "创建订单",
			LogType:   1,
			OrderId:   id,
			CreatedAt: gtime.Now(),
			ViewPaht:  "/usercenterOrder/index",
			Parame:    string(gjson.MustEncode(g.Map{"route": "/userLogs/usercenterUserrequestLog/index", "query": g.Map{"tid": req.Tid}})),
		}
		_, err = dao.UsercenterOrderLog.Ctx(ctx).TX(tx).OmitEmpty().Data(orderlog).Insert()
		if err != nil {
			return gerror.Wrap(err, "订单日志插入失败")
		}
		// 更新 usercenter_pay_config 表，不处理错误
		g.Model("usercenter_pay_config").
			Where("id", qudao["id"].Int()).
			Data(payconfgdata).
			Update()
		return
	})

	if err != nil {
		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogRequest(ctx, "事务执行失败", tradeNo, req.Tid, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}

	// 初始化 res
	service.PayApi().LogRequest(ctx, "请求成功", tradeNo, req.Tid, 0, 1, uonst.INFO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
	res = &payapi.CreateOrderOup{}
	res.Appid = req.Appid
	res.ApplyTime = gtime.Now()
	res.MerchantTradeno = req.Tid
	res.Money = gconv.Float64(req.Money)
	res.Payurl = payurl
	res.Tradeno = tradeNo
	// 将结构体 res 转为 map[string]interface{}
	resultdata := gconv.Map(res)

	// 将 map[string]interface{} 转换为 map[string]string
	resultStrData := gconv.MapStrStr(resultdata)
	sign, err := service.PayApi().CheckSign(ctx, resultStrData, mb)
	res.Sign = sign
	return
}
func (d *Jjpay) CreateOrderto(ctx context.Context, order *entity.UsercenterOrder, mb *entity.UsercenterUser, qudao gdb.Record, feedata payapi.QudaoList) (res *struct{}, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// 示例数据

	tradeNo := order.SysOrderno

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{
		"ChannelCode":  strings.TrimSpace(PayConfig["order_passage_code"]),
		"McorderNo":    tradeNo,
		"passage_code": strings.TrimSpace(PayConfig["order_passage_code"]),
		"Type":         "INR",
		"Amount":       order.Money,
	}

	// 假设这是当前的域名获取
	domain := "https://" + g.Cfg().MustGet(ctx, "payapi.apidomain").String()
	callbackURL := domain + "/api/payapi/v1/dsnotify/Jjpay"

	// 添加 notifyurl 和 types
	sfdata["CallBackUrl"] = callbackURL
	sfdata["JumpUrl"] = "https://www.google.com"
	// 对 sfdata 按键名进行排序
	keys := make([]string, 0, len(sfdata))
	for k := range sfdata {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/create", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/create", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道配置错，误请联系管理员", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, requestData, "", 0, qudao["id"].Int64())
		return
	}
	// 读取响应内容
	content := response.ReadAllString()

	defer response.Close()
	endTime := gtime.Now()
	duration := endTime.Sub(startTime)
	seconds := int64(duration.Seconds()) // 获取时间差的秒数
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		service.PayApi().LogRequest(ctx, "通道响应错误", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		service.PayApi().LogRequest(ctx, "上游响应json格式错误", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return

	}
	code := result["code"].(float64)
	// code, ok := result["code"].(int64)
	// if ok {
	// 	if code==0{
	// 		err = gerror.New("响应数据类型错误")
	// 	return
	// 	}
	// }else{
	// 	err = gerror.New("响应数据类型错误")
	// 	return
	// }
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		service.PayApi().LogRequest(ctx, msg, tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}
	// var payurl, tradeNo string
	var payurl, ThirdOrderno string
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 获取 payurl
		payurl = data["payUrl"].(string)

		// 获取 TradeNo
		ThirdOrderno = data["orderNo"].(string)

	} else {
		err = gerror.New("data 字段不存在或解析失败")
		service.PayApi().LogRequest(ctx, "data 字段不存在或解析失败", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}
	// 输出最终的 sfdata

	// 先来更新比树和成功率
	ds_cgl := qudao["ds_cgl"].Int()
	ds_hatnum := qudao["ds_hatnum"].Int() + 1
	if qudao["ds_success"].Int() != 0 {
		ds_cgl = int(qudao["ds_success"].Float64() / float64(ds_hatnum) * 100)
	}
	payconfgdata := g.Map{
		"ds_hatnum": ds_hatnum,
		"ds_cgl":    ds_cgl,
	}

	// 使用事务处理订单创建逻辑
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		// 直接更新订单，不再检查是否存在
		orderData := &entity.UsercenterOrder{
			ThirdOrderno: ThirdOrderno,
			PaytmId:      qudao["id"].Int64(),
			Payurl:       payurl,
			IsRequest:    1,
		}
		if _, err := dao.UsercenterOrder.Ctx(ctx).TX(tx).OmitEmpty().Data(orderData).Where("sys_orderno", order.SysOrderno).Update(); err != nil {
			return gerror.Wrap(err, "订单更新失败")
		}
		// 确保订单插入了再增加日报数量

		err = userver.CommonService().UpdateUserReportInTx(ctx, tx, &entity.UsercenterUserReport{
			UserId: int64(mb.Id),
			DsOpen: 1,
		})
		if err != nil {
			return gerror.Wrap(err, "更新日报失败")
		}
		// 更新 usercenter_pay_config 表
		if _, err := g.Model("usercenter_pay_config").
			Where("id", qudao["id"].Int()).
			Data(payconfgdata).
			Update(); err != nil {
			return gerror.Wrap(err, "更新渠道配置失败")
		}
		return
	})

	if err != nil {
		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogRequest(ctx, "事务执行失败", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}

	// 初始化 res
	if err != nil {
		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogRequest(ctx, "事务执行失败", tradeNo, order.UserOrderno, 0, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())
		return
	}

	// 初始化 res
	service.PayApi().LogRequest(ctx, "请求成功", tradeNo, order.UserOrderno, 0, 1, uonst.INFO, err, gconv.Map(sfdata), content, seconds, qudao["id"].Int64())

	return
}
func (d *Jjpay) Queryorder(ctx context.Context, req *payapi.QueryorderInp, mb *entity.UsercenterUser, qudao gdb.Record, od *entity.UsercenterOrder) (res *payapi.QueryorderOup, err error) {
	// 假设这里处理订单创建逻辑
	// 第一步查询支付渠道
	// fmt.Printf("Dspay: 处理订单%+v", req)
	// fmt.Printf("Dspay: 处理用户%+v", mb)
	// fmt.Printf("Dspay: 处理渠道%+v", qudao)
	// 解析参数配置
	// 定义一个空的 map 用于存储 key-value 对
	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 首先查本地状态 如果本地状态是待审才会真的去查询上游
	if od.Status != 0 {
		// 初始化 res
		// var payurl, tradeNo string
		var Ttradestatus, Utr string
		if od.Status == 1 {
			Ttradestatus = consts.PAYMENT_SUCCESS
		} else if od.Status == 2 {
			Ttradestatus = consts.PAYMENT_FAILURE
		} else {
			Ttradestatus = consts.PAYMENT_FAILURE
		}
		CompleteTime := *od.UpdatedAt
		res = &payapi.QueryorderOup{}
		res.Appid = req.Appid
		res.ApplyTime = od.CreatedAt
		res.MerchantTradeno = od.UserOrderno
		res.Money = gconv.String(fmt.Sprintf("%.2f", od.Money))

		res.CompleteTime = &CompleteTime
		res.Tradeno = od.SysOrderno
		res.Ttradestatus = Ttradestatus
		res.Utr = Utr
		// 将结构体 res 转为 map[string]interface{}
		resultdata := gconv.Map(res)

		// 将 map[string]interface{} 转换为 map[string]string
		resultStrData := gconv.MapStrStr(resultdata)
		sign, nerr := service.PayApi().CheckSign(ctx, resultStrData, mb)
		if nerr != nil {
			err = gerror.Wrap(nerr, "签名生成错误")
			return
		}
		res.Sign = sign
		return
	}
	// 构造请求到上游
	// 示例数据

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"orderNo": od.SysOrderno,
	}

	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/queryorder", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/queryorder", sfdata)

	if err != nil {
		err = gerror.New("通道响应错误")
		return
	}
	defer response.Close()
	// 读取响应内容
	content := response.ReadAllString()
	fmt.Printf("请求详细%v", content)
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		return

	}
	code := result["code"].(float64)
	fmt.Printf("看一下查询结果%v", code)
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		return
	}
	// var payurl, tradeNo string
	var Ttradestatus, Utr string
	var CompleteTime gtime.Time

	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 获取 payurl
		if data["status"].(string) == "created" || data["status"].(string) == "paying" {
			Ttradestatus = consts.WAITING_PAYMENT
		} else if data["status"].(string) == "success" {
			Ttradestatus = consts.PAYMENT_SUCCESS
		} else {
			Ttradestatus = consts.PAYMENT_FAILURE
		}
		CompleteTime = *od.UpdatedAt
		if utrVal, ok := data["utr"]; ok {
			if utrStr, isString := utrVal.(string); isString {
				Utr = utrStr

			}
		}
	} else {
		err = gerror.New("data 字段不存在或解析失败")
		return
	}

	// 初始化 res

	res = &payapi.QueryorderOup{}
	res.Appid = req.Appid
	res.ApplyTime = od.CreatedAt
	res.MerchantTradeno = od.UserOrderno
	res.Money = gconv.String(fmt.Sprintf("%.2f", od.Money))

	res.CompleteTime = &CompleteTime
	res.Tradeno = od.SysOrderno
	res.Ttradestatus = Ttradestatus
	res.Utr = Utr
	// 将结构体 res 转为 map[string]interface{}
	resultdata := gconv.Map(res)

	// 将 map[string]interface{} 转换为 map[string]string
	resultStrData := gconv.MapStrStr(resultdata)
	sign, err := service.PayApi().CheckSign(ctx, resultStrData, mb)
	res.Sign = sign
	return

}
func (d *Jjpay) DsNotify(ctx context.Context, req map[string]interface{}) (res *payapi.Notifyoup, err error) {
	r := g.RequestFromCtx(ctx) // 获取当前请求对象 ghttp.Request
	// 记录原始请求信息
	g.Log().Debugf(ctx, "请求方法: %s", r.Method)
	g.Log().Debugf(ctx, "请求头: %v", r.Header)
	g.Log().Debugf(ctx, "请求体: %s", string(r.GetBody()))
	// 将请求参数转为JSON格式并打印
	// 尝试多种方式解析数据
	var data map[string]interface{}

	// 1. 先尝试获取已解析的数据
	if len(req) > 0 {
		data = req
	} else {
		// 2. 尝试JSON解析
		if jsonData, err := r.GetJson(); err == nil && jsonData != nil {
			data = gconv.Map(jsonData)
		}

		// 3. 如果JSON解析失败，尝试表单数据
		if len(data) == 0 {
			if formData := r.GetForm(""); formData != nil {
				data = gconv.Map(formData)
			}
		}

		// 4. 如果表单数据也没有，尝试URL参数
		if len(data) == 0 {
			if queryData := r.GetQuery(""); queryData != nil {
				data = gconv.Map(queryData)
			}
		}

		// 5. 最后尝试直接解析请求体
		if len(data) == 0 {
			body := r.GetBody()
			if len(body) > 0 {
				// 尝试直接解析原始数据
				if err := json.Unmarshal(body, &data); err != nil {
					// 如果JSON解析失败，尝试解析为字符串
					strData := string(body)
					if strData != "" {
						data = g.Map{
							"raw_data": strData,
						}
					}
				}
			}
		}
	}

	// 记录解析后的数据
	g.Log().Debugf(ctx, "解析后的数据: %v", data)

	if len(data) == 0 {
		return nil, gerror.New("无法解析请求数据")
	}
	// 先来查询请求的订单号
	out_trade_no := req["merchantorder"]
	// 通过系统单号查询订单
	var od *entity.UsercenterOrder
	if err = dao.UsercenterOrder.Ctx(ctx).Where("sys_orderno", out_trade_no).Scan(&od); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		return
	}
	if od == nil {

		err = gerror.New("订单不存在")
		service.PayApi().LogNotify(ctx, "订单不存在", out_trade_no.(string), "", 0, 0, uonst.ERRO, err, req)

		return
	} // 先检查用户存在不存在
	var mb *entity.UsercenterUser
	if err = dao.UsercenterUser.Ctx(ctx).Where("id", od.UserId).Scan(&mb); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		service.PayApi().LogNotify(ctx, "sql查询错误", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return
	}
	if mb == nil {
		err = gerror.New("用户错误或已删除")
		service.PayApi().LogNotify(ctx, "用户错误或已删除", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return
	}
	// 获取该渠道配置信息
	var qudao *entity.UsercenterPayConfig
	if err = dao.UsercenterPayConfig.Ctx(ctx).Where("id", od.PaytmId).Scan(&qudao); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		return
	}
	if qudao == nil {
		err = gerror.New("通道不存在")
		service.PayApi().LogNotify(ctx, "通道不存在或者已删除", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return
	}

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		service.PayApi().LogNotify(ctx, "渠道配置错，误请联系管理员", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.WARN, err, req)

		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		service.PayApi().LogNotify(ctx, "渠道配置错，误请联系管理员", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return
	}
	fmt.Printf("回调的json数据%v\n 完整req回调数据%v", req["sign"], req)
	// map是值传递所以要拷贝
	sfdata := make(map[string]interface{})
	for k, v := range req {
		sfdata[k] = v
	}
	// 移除 token

	fmt.Printf("检查传递后的数据%v\n 完整req回调数据%v", r.Header.Get("nonce"), req)
	// 获取所有 Header 值

	sfSign := generateSignature("POST", "/api/payapi/v1/dsnotify/Jjpay", PayConfig["accessKey"], PayConfig["accessSecret"], r.Header.Get("timestamp"), r.Header.Get("nonce"))

	// 将签名加入 sfdata
	if r.Header.Get("sign") != sfSign {
		err = gerror.New("签名错误")
		service.PayApi().LogNotify(ctx, "签名错误", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return
	}

	if od.Status != 0 {

		err = gerror.New("订单已修改")
		service.PayApi().LogNotify(ctx, "订单状态已修改", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return

	}
	// 插入订单日志
	orderlog := &entity.UsercenterOrderLog{
		Orderno:   od.SysOrderno,
		CreatedBy: 0,
		Event:     "上游回调",
		LogType:   1,
		OrderId:   od.Id,
		CreatedAt: gtime.Now(),
		ViewPaht:  "/usercenterOrder/index",
		Parame:    string(gjson.MustEncode(g.Map{"route": "/userLogs/usercenterUpserveLog/index", "query": g.Map{"oid": od.SysOrderno}})),
	}

	_ = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, _ = dao.UsercenterOrderLog.Ctx(ctx).TX(tx).OmitEmpty().Data(orderlog).Insert()
		// 总是返回nil,即使发生错误
		return nil
	})
	Ttradestatus := consts.WAITING_PAYMENT
	qudao_success := qudao.DfSuccess
	var order_status int

	//开启事务更新订单查询
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		var releorder gdb.Record // 声明变量
		releorder, err = tx.Model("usercenter_order").
			WherePri(od.Id).
			LockUpdate(). // 行锁，确保并发安全
			One()
		if err != nil {
			err = gerror.Wrap(err, "查询订单号失败")
			service.PayApi().LogNotify(ctx, "查询订单号失败", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

			return

		}
		// 如果查询到的订单不为空，说明订单号重复
		if releorder == nil {

			err = gerror.New("订单不存在")
			service.PayApi().LogNotify(ctx, "订单不存在", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

			return
		}
		if releorder["status"].Int() != 0 {

			err = gerror.New("订单已处理")
			service.PayApi().LogNotify(ctx, "订单已处理", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

			return
		}
		// 将 Money 从 string 转换为 float64
		// 获取 req["money"]，并判断其类型是否为 string，如果是，则进行转换为 float64
		moneyStr, ok := req["amount"].(string)
		if ok {
			// 将字符串类型的 money 转换为 float64
			moneyFloat, _ := strconv.ParseFloat(moneyStr, 64)

			// 比较 od.Money 和 转换后的 moneyFloat
			if strconv.FormatFloat(od.Money, 'f', 2, 64) != strconv.FormatFloat(moneyFloat, 'f', 2, 64) {
				err = gerror.New("金额转换失败")
				service.PayApi().LogNotify(ctx, "金额转换失败", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

				return

			}
		} else {
			// 如果 money 已经是 float64，则直接比较
			fmt.Printf("req对的类型%+v", req)
			if strconv.FormatFloat(od.Money, 'f', 2, 64) != strconv.FormatFloat(gconv.Float64(req["amount"]), 'f', 2, 64) {
				err = gerror.New("金额转换不正确")
				service.PayApi().LogNotify(ctx, "金额转换不正确", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

				return
			}
		}
		status, ok := req["status"].(string)
		if ok {
			if status == "success" {
				order_status = 1
				Ttradestatus = consts.PAYMENT_SUCCESS
				qudao_success = qudao_success + 1
			} else {
				order_status = 2
				Ttradestatus = consts.PAYMENT_FAILURE
			}
		}
		orderupdata := g.Map{
			"status": order_status,
		}
		// 更新 usercenter_pay_config 表，不处理错误
		tx.Model("usercenter_order").
			WherePri(od.Id).
			Data(orderupdata).
			Update()
		return
	})

	if err != nil {
		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogNotify(ctx, "事务执行失败", out_trade_no.(string), od.UserOrderno, 0, 0, uonst.ERRO, err, req)

		return

	}
	// 更新成功率
	// 先来更新比树和成功率
	// 获取回调得订单状态
	utr, ok := req["proof"].(string)
	if !ok {
		utr = ""
	}
	if Ttradestatus == consts.PAYMENT_SUCCESS {
		userver.CommonService().ProcessOrderUpdate(ctx, od, 1, utr, "系统自动回调", "System", 0, "")
	} else if Ttradestatus == consts.PAYMENT_FAILURE {
		// 不扣除手续费  修改订单状态为失败 发送回调
		userver.CommonService().ProcessOrderUpdate(ctx, od, 2, utr, "系统自动回调", "System", 0, "")
	}
	service.PayApi().LogNotify(ctx, "回调成功", out_trade_no.(string), od.UserOrderno, 0, 1, uonst.ERRO, err, req)
	res = &payapi.Notifyoup{}
	res.Status = 1
	res.Message = "SUCCESS"

	return
	// response.RText(g.RequestFromCtx(ctx), "success")

}
func (d *Jjpay) Withdraw(ctx context.Context, od *entity.UsercenterPayment, mb *entity.UsercenterUser, paytm *entity.UsercenterPayConfig) (res interface{}, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(paytm.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道配置错，误请联系管理员", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, requestData, "", 0, paytm.Id)

		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}

	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["payment_passage_code"] == "" {

		err = gerror.New("渠道参数错误，误请联系管理员")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道参数错误，误请联系管理员", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, requestData, "", 0, paytm.Id)

		return
	}
	// 构造请求到上游
	// 示例数据

	tradeNo := od.SysOrderno

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"ChannelCode": strings.TrimSpace(PayConfig["payment_passage_code"]),
		"amount":      fmt.Sprintf("%.2f", od.Money),
		"McorderNo":   tradeNo,
		"Type":        "inr",
		"Address":     "",
		"name":        od.UserName,
		"ifsc":        od.Ifsc,
		"BankName":    od.Bankname,
		"BankAccount": od.Cardbanck,
	}
	// 假设这是当前的域名获取
	doman := g.Cfg().MustGet(ctx, "payapi.domain").String() // 您可以通过其他方式获取
	callbackURL := doman + "/api/payapi/v1/dfnotify/Jjpay"
	// 添加 notifyurl 和 types
	sfdata["NotifyUrl"] = callbackURL

	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)

	nonce := generateRandomString(6)

	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/payorder/create", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/payorder/create", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")

		service.PayApi().LogRequest(ctx, "通道响应错误", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), "", 0, paytm.Id)

		return
	}
	// 读取响应内容
	content := response.ReadAllString()
	endTime := gtime.Now()
	duration := endTime.Sub(startTime)
	seconds := int64(duration.Seconds()) // 获取时间差的秒数

	defer response.Close()
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {

		err = gerror.New("请求上游状态不成功")
		service.PayApi().LogRequest(ctx, "通道响应错误", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	fmt.Printf("实际响应内容:%+v", result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		service.PayApi().LogRequest(ctx, "上游未知错误，msg无法获取", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return

	}
	code := result["code"].(float64)
	// code, ok := result["code"].(int64)
	// if ok {
	// 	if code==0{
	// 		err = gerror.New("响应数据类型错误")
	// 	return
	// 	}
	// }else{
	// 	err = gerror.New("响应数据类型错误")
	// 	return
	// }
	msg := result["message"].(string)
	// 输出最终的 sfdata
	fmt.Printf("最终的请求结果状态: %+v\n 响应内容:%+v\n CoDE实际值 code%v\n msg的值:%v \n 响应:%v \n", response.StatusCode, response.Body, code, msg, content)
	if code != 200 {

		err = gerror.New(msg)
		service.PayApi().LogRequest(ctx, msg, od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}
	// var payurl, tradeNo string
	var ThirdOrderno string
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {

		// 获取 TradeNo
		ThirdOrderno = data["orderNo"].(string)

	} else {
		err = gerror.New("data 字段不存在或解析失败")
		service.PayApi().LogRequest(ctx, "data 字段不存在或解析失败", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)

		return
	}

	// 先来更新比树和成功率
	df_cgl := paytm.DfCgl
	df_hatnum := paytm.DfHatnum + 1
	if paytm.DfSuccess != 0 {

		df_cgl = int(float64(paytm.DfSuccess) / float64(df_hatnum) * 100)
	}
	payconfgdata := g.Map{
		"df_hatnum": df_hatnum,
		"df_cgl":    df_cgl,
	}
	// 记录日报

	// 使用事务处理订单创建逻辑
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		orderData := &entity.UsercenterPayment{

			ThirdOrderno: ThirdOrderno,
			IsTuitsong:   1,
		}
		fmt.Printf("看看要更新的Id%v", ThirdOrderno)

		if _, err := dao.UsercenterPayment.Ctx(ctx).TX(tx).OmitEmpty().Where("id", od.Id).Data(orderData).Update(); err != nil {
			return gerror.Wrap(err, "订单更新失败")
		}
		// 确保订单插入了再增加日报数量

		err = userver.CommonService().UpdateUserReportInTx(ctx, tx, &entity.UsercenterUserReport{
			UserId: int64(mb.Id),
		})
		if err != nil {
			return gerror.Wrap(err, "更新日报失败")
		}
		// 更新 usercenter_pay_config 表，不处理错误
		g.Model("usercenter_pay_config").
			Where("id", paytm.Id).
			Data(payconfgdata).
			Update()
		return
	})

	if err != nil {

		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogRequest(ctx, "事务执行失败", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}

	res = map[string]interface{}{
		"status":  "success",
		"orderNo": od.SysOrderno,
	}
	service.PayApi().LogRequest(ctx, "请求成功", od.SysOrderno, od.UserOrderno, 1, 1, uonst.INFO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
	return result, nil
}
func (d *Jjpay) Withdrawupi(ctx context.Context, od *entity.UsercenterPayment, mb *entity.UsercenterUser, paytm *entity.UsercenterPayConfig) (res interface{}, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(paytm.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道配置错，误请联系管理员", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, requestData, "", 0, paytm.Id)

		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}

	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {

		err = gerror.New("渠道参数错误，误请联系管理员")
		requestData := make(map[string]interface{})
		service.PayApi().LogRequest(ctx, "渠道参数错误，误请联系管理员", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, requestData, "", 0, paytm.Id)

		return
	}
	// 构造请求到上游
	// 示例数据

	tradeNo := od.SysOrderno

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"ChannelCode": strings.TrimSpace(PayConfig["payment_passage_code"]),
		"amount":      fmt.Sprintf("%.2f", od.Money),
		"McorderNo":   tradeNo,
		"Type":        "inr",
		"Address":     "",
		"name":        od.UserName,
		"Ifsc":        "UPI00000000",
		"BankName":    "UPI",
		"BankAccount": od.Cardbanck,
	}
	// 假设这是当前的域名获取
	doman := g.Cfg().MustGet(ctx, "payapi.domain").String() // 您可以通过其他方式获取
	callbackURL := doman + "/api/payapi/v1/dfnotify/Uforpay"
	// 添加 notifyurl 和 types
	sfdata["NotifyUrl"] = callbackURL

	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)

	nonce := generateRandomString(6)

	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/payorder/create", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/payorder/create", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")

		service.PayApi().LogRequest(ctx, "通道响应错误", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), "", 0, paytm.Id)

		return
	}
	// 读取响应内容
	content := response.ReadAllString()
	endTime := gtime.Now()
	duration := endTime.Sub(startTime)
	seconds := int64(duration.Seconds()) // 获取时间差的秒数

	defer response.Close()
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {

		err = gerror.New("请求上游状态不成功")
		service.PayApi().LogRequest(ctx, "通道响应错误", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	fmt.Printf("实际响应内容:%+v", result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		service.PayApi().LogRequest(ctx, "上游未知错误，msg无法获取", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return

	}
	code := result["code"].(float64)
	// code, ok := result["code"].(int64)
	// if ok {
	// 	if code==0{
	// 		err = gerror.New("响应数据类型错误")
	// 	return
	// 	}
	// }else{
	// 	err = gerror.New("响应数据类型错误")
	// 	return
	// }
	msg := result["message"].(string)
	// 输出最终的 sfdata
	fmt.Printf("最终的请求结果状态: %+v\n 响应内容:%+v\n CoDE实际值 code%v\n msg的值:%v \n 响应:%v \n", response.StatusCode, response.Body, code, msg, content)
	if code != 200 {

		err = gerror.New(msg)
		service.PayApi().LogRequest(ctx, msg, od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}
	// var payurl, tradeNo string
	var ThirdOrderno string
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {

		// 获取 TradeNo
		ThirdOrderno = data["orderNo"].(string)

	} else {
		err = gerror.New("data 字段不存在或解析失败")
		service.PayApi().LogRequest(ctx, "data 字段不存在或解析失败", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)

		return
	}

	// 先来更新比树和成功率
	df_cgl := paytm.DfCgl
	df_hatnum := paytm.DfHatnum + 1
	if paytm.DfSuccess != 0 {

		df_cgl = int(float64(paytm.DfSuccess) / float64(df_hatnum) * 100)
	}
	payconfgdata := g.Map{
		"df_hatnum": df_hatnum,
		"df_cgl":    df_cgl,
	}
	// 记录日报

	// 使用事务处理订单创建逻辑
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		orderData := &entity.UsercenterPayment{

			ThirdOrderno: ThirdOrderno,
			IsTuitsong:   1,
		}
		fmt.Printf("看看要更新的Id%v", ThirdOrderno)

		if _, err := dao.UsercenterPayment.Ctx(ctx).TX(tx).OmitEmpty().Where("id", od.Id).Data(orderData).Update(); err != nil {
			return gerror.Wrap(err, "订单更新失败")
		}
		// 确保订单插入了再增加日报数量

		err = userver.CommonService().UpdateUserReportInTx(ctx, tx, &entity.UsercenterUserReport{
			UserId: int64(mb.Id),
		})
		if err != nil {
			return gerror.Wrap(err, "更新日报失败")
		}
		// 更新 usercenter_pay_config 表，不处理错误
		g.Model("usercenter_pay_config").
			Where("id", paytm.Id).
			Data(payconfgdata).
			Update()
		return
	})

	if err != nil {

		err = gerror.Wrap(err, "事务执行失败")
		service.PayApi().LogRequest(ctx, "事务执行失败", od.SysOrderno, od.UserOrderno, 1, 0, uonst.ERRO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
		return
	}

	res = map[string]interface{}{
		"status":  "success",
		"orderNo": od.SysOrderno,
	}
	service.PayApi().LogRequest(ctx, "请求成功", od.SysOrderno, od.UserOrderno, 1, 1, uonst.INFO, err, gconv.Map(sfdata), content, seconds, paytm.Id)
	return result, nil
}

// 查询代付
func (d *Jjpay) QueryWithdraw(ctx context.Context, req *payapi.QueryorderInp, mb *entity.UsercenterUser, qudao gdb.Record, od *entity.UsercenterPayment) (res *payapi.QueryorderOup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["payment_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// var payurl, tradeNo string
	var Ttradestatus, Utr string
	var CompleteTime gtime.Time

	// 示例数据
	if od.Status == 0 {

		// 构造请求创建 sfdata 的 map
		sfdata := g.Map{

			"orderNo": strings.TrimSpace(od.SysOrderno),
		}

		nonce := generateRandomString(6)
		startTime := gtime.Now()
		// 带超时时间设置的请求方式 r
		// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
		response, nere := g.Client().Header(map[string]string{
			"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
			"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
			"nonce":        nonce,                                // 添加 Content-Type 头
			"sign":         generateSignature("POST", "/api/payorder/queryorder", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
			"Content-Type": "application/json",
		}).Post(ctx, d.GetBaseUrl()+"/api/payorder/queryorder", sfdata)

		if nere != nil {
			err = gerror.Wrap(nere, "通道响应错误")
			return

		}
		fmt.Printf("要查询的代付订单号%v", od.SysOrderno)
		defer response.Close()
		// 如果请求响应状态不为200
		if response.StatusCode != http.StatusOK {
			err = gerror.New("请求上游状态不成功")
			return
		}
		fmt.Printf("错误内容第一步-------4")
		// 读取响应内容
		content := response.ReadAllString()
		// 使用 map[string]interface{} 来解析不确定的 JSON 结构
		var result map[string]interface{}
		err = json.Unmarshal([]byte(content), &result)
		if err != nil {
			err = gerror.New("解析 JSON 错误:")
			return

		}
		code := result["code"].(float64)

		msg, ok := result["message"].(string)
		if !ok || msg == "" {
			msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
		}
		if code != 200 {
			err = gerror.New(msg)
			return
		}

		// 获取 data 中的内容
		if data, ok := result["result"].(map[string]interface{}); ok {
			// 获取 payurl
			// 获取 payurl
			if data["status"].(string) == "created" || data["status"].(string) == "paying" {
				Ttradestatus = consts.WAITING_PAYMENT
			} else if data["status"].(string) == "success" {
				Ttradestatus = consts.PAYMENT_SUCCESS
			} else {
				// Ttradestatus = consts.WITHDRAWAL_FAILURE
				// 如果上游失败我们依然返回待审
				Ttradestatus = consts.WAITING_WITHDRAWAL
			}
			if od.Status == 3 {
				Ttradestatus = consts.WAITING_WITHDRAWAL
			}
			CompleteTime = *od.UpdatedAt
			if utrVal, ok := data["utr"]; ok {
				if utrStr, isString := utrVal.(string); isString {
					Utr = utrStr

				}
			}
		} else {
			err = gerror.New("data 字段不存在或解析失败")
			return
		}

	} else {
		if od.Status == 1 {
			Ttradestatus = consts.WITHDRAWAL_SUCCESS

		} else if od.Status == 2 {
			Ttradestatus = consts.WITHDRAWAL_FAILURE
		} else if od.Status == 3 {
			Ttradestatus = consts.WAITING_WITHDRAWAL
		}
		Utr = od.Utr
	}
	if err != nil {
		err = gerror.New("支付模块失败")
		return
	}
	// 初始化 res

	res = &payapi.QueryorderOup{}
	res.Appid = req.Appid
	res.ApplyTime = od.CreatedAt
	res.MerchantTradeno = od.UserOrderno
	res.Money = gconv.String(fmt.Sprintf("%.2f", od.Money))

	res.CompleteTime = &CompleteTime
	res.Tradeno = od.SysOrderno
	res.Ttradestatus = Ttradestatus
	res.Utr = Utr
	// 将结构体 res 转为 map[string]interface{}
	resultdata := gconv.Map(res)

	// 将 map[string]interface{} 转换为 map[string]string
	resultStrData := gconv.MapStrStr(resultdata)
	sign, err := service.PayApi().CheckSign(ctx, resultStrData, mb)
	res.Sign = sign
	return

}

func (d *Jjpay) DfNotify(ctx context.Context, req map[string]interface{}) (res *payapi.Notifyoup, err error) {
	r := g.RequestFromCtx(ctx) // 获取当前请求对象 ghttp.Request
	// 记录原始请求信息
	g.Log().Debugf(ctx, "请求方法: %s", r.Method)
	g.Log().Debugf(ctx, "请求头: %v", r.Header)
	g.Log().Debugf(ctx, "请求体: %s", string(r.GetBody()))
	// 将请求参数转为JSON格式并打印
	// 尝试多种方式解析数据
	var data map[string]interface{}

	// 1. 先尝试获取已解析的数据
	if len(req) > 0 {
		data = req
	} else {
		// 2. 尝试JSON解析
		if jsonData, err := r.GetJson(); err == nil && jsonData != nil {
			data = gconv.Map(jsonData)
		}

		// 3. 如果JSON解析失败，尝试表单数据
		if len(data) == 0 {
			if formData := r.GetForm(""); formData != nil {
				data = gconv.Map(formData)
			}
		}

		// 4. 如果表单数据也没有，尝试URL参数
		if len(data) == 0 {
			if queryData := r.GetQuery(""); queryData != nil {
				data = gconv.Map(queryData)
			}
		}

		// 5. 最后尝试直接解析请求体
		if len(data) == 0 {
			body := r.GetBody()
			if len(body) > 0 {
				// 尝试直接解析原始数据
				if err := json.Unmarshal(body, &data); err != nil {
					// 如果JSON解析失败，尝试解析为字符串
					strData := string(body)
					if strData != "" {
						data = g.Map{
							"raw_data": strData,
						}
					}
				}
			}
		}
	}

	// 记录解析后的数据
	g.Log().Debugf(ctx, "解析后的数据: %v", data)

	if len(data) == 0 {
		return nil, gerror.New("无法解析请求数据")
	}
	// 先来查询请求的订单号
	out_trade_no := req["merchantorder"]
	// 先来查询请求的订单号

	// 通过系统单号查询订单
	var od *entity.UsercenterPayment
	if err = dao.UsercenterPayment.Ctx(ctx).Where("sys_orderno", out_trade_no).Scan(&od); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		return
	}
	if od == nil {

		err = gerror.New("订单不存在")
		service.PayApi().LogNotify(ctx, "订单不存在", out_trade_no.(string), "", 1, 0, uonst.ERRO, err, req)

		return
	} // 先检查用户存在不存在
	var mb *entity.UsercenterUser
	if err = dao.UsercenterUser.Ctx(ctx).Where("id", od.UserId).Scan(&mb); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		service.PayApi().LogNotify(ctx, "sql查询错误", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return
	}
	if mb == nil {
		err = gerror.New("用户错误或已删除")
		service.PayApi().LogNotify(ctx, "用户错误或已删除", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return
	}
	// 获取该渠道配置信息
	var qudao *entity.UsercenterPayConfig
	if err = dao.UsercenterPayConfig.Ctx(ctx).Where("id", od.PaytmId).Scan(&qudao); err != nil {
		err = gerror.Wrap(err, "sql查询错误")
		return
	}
	if qudao == nil {
		err = gerror.New("通道不存在")
		service.PayApi().LogNotify(ctx, "通道不存在或者已删除", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return
	}

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		service.PayApi().LogNotify(ctx, "渠道配置错，误请联系管理员", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.WARN, err, req)

		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["payment_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		service.PayApi().LogNotify(ctx, "渠道配置错，误请联系管理员", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return
	}

	sfSign := generateSignature("POST", "/api/payapi/v1/dfnotify/Jjpay", PayConfig["accessKey"], PayConfig["accessSecret"], r.Header.Get("timestamp"), r.Header.Get("nonce"))

	// 将签名加入 sfdata
	if r.Header.Get("sign") != sfSign {
		err = gerror.New("签名错误")
		service.PayApi().LogNotify(ctx, "签名错误", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return
	}
	if od.Status != 0 {

		err = gerror.New("订单已修改")
		service.PayApi().LogNotify(ctx, "订单状态已修改", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

		return

	}
	var order_status int
	is_tuitsong := 0
	// 获取 req["money"]，并判断其类型是否为 string，如果是，则进行转换为 float64
	moneyStr, ok := req["amount"].(string)
	if ok {
		// 将字符串类型的 money 转换为 float64
		moneyFloat, _ := strconv.ParseFloat(moneyStr, 64)

		// 比较 od.Money 和 转换后的 moneyFloat
		if strconv.FormatFloat(od.Money, 'f', 2, 64) != strconv.FormatFloat(moneyFloat, 'f', 2, 64) {
			err = gerror.New("金额转换失败")
			service.PayApi().LogNotify(ctx, "金额转换失败", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

			return

		}
	} else {
		// 如果 money 已经是 float64，则直接比较
		if strconv.FormatFloat(od.Money, 'f', 2, 64) != strconv.FormatFloat(gconv.Float64(req["amount"]), 'f', 2, 64) {
			err = gerror.New("金额转换不正确")
			service.PayApi().LogNotify(ctx, "金额转换不正确", out_trade_no.(string), od.UserOrderno, 1, 0, uonst.ERRO, err, req)

			return
		}
	}
	status, ok := req["status"].(string)
	if ok {
		if status == "success" {
			order_status = 1
			is_tuitsong = 1
		} else {
			// 根据代付回调模式配置来决定失败时的处理方式
			commonConfig, configErr := isc.SysConfig().GetCommon(ctx)
			if configErr == nil && commonConfig != nil {
				if commonConfig.CallbackMode == 1 {
					// 人工处理模式：设置为退回单等待人工处理
					order_status = 3
				} else {
					// 自动处理模式：设置为失败状态
					order_status = 2
				}
			} else {
				// 获取配置失败时，默认使用退回单模式
				order_status = 3
			}
			is_tuitsong = 1
		}
	}
	utr, ok := req["proof"].(string)
	if !ok {
		utr = ""
	}
	//开启事务更新订单查询
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		var releorder gdb.Record // 声明变量
		releorder, err = tx.Model("usercenter_payment").
			WherePri(od.Id).
			LockUpdate(). // 行锁，确保并发安全
			One()
		if err != nil {
			err = gerror.Wrap(err, "查询订单号失败")

			return

		}
		// 如果查询到的订单不为空，说明订单号重复
		if releorder == nil {

			err = gerror.New("订单不存在")

			return
		}
		// if releorder["status"].Int() != 0 {

		// 	err = gerror.New("订单已处理")

		// 	return
		// }
		// 新增

		orderupdata := g.Map{
			"status":      order_status,
			"is_tuitsong": is_tuitsong,
			"utr":         utr,

			"created_by": 0,
		}
		// 更新 usercenter_pay_config 表，不处理错误
		tx.Model("usercenter_payment").
			WherePri(od.Id).
			Data(orderupdata).
			Update()
		return
	})
	if err != nil {
		err = gerror.Wrap(err, "事务执行失败")

		return

	}
	if order_status == 3 {
		err = gerror.Wrap(err, "回调失败改成退回单")
		// 插入订单日志
		orderlog := &entity.UsercenterOrderLog{
			Orderno:   od.SysOrderno,
			CreatedBy: 0,
			Event:     "回调失败改成退回单",
			LogType:   2,
			OrderId:   od.Id,
			CreatedAt: gtime.Now(),
			ViewPaht:  "/usercenterOrder/index",
			Parame:    string(gjson.MustEncode(g.Map{"route": "/userLogs/usercenterUpserveLog/index", "query": g.Map{"oid": od.SysOrderno}})),
		}

		_ = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			_, _ = dao.UsercenterOrderLog.Ctx(ctx).TX(tx).OmitEmpty().Data(orderlog).Insert()
			// 总是返回nil,即使发生错误
			return nil
		})
		service.PayApi().LogNotify(ctx, "回调失败改成退回单", out_trade_no.(string), od.UserOrderno, 1, 1, uonst.ERRO, err, req)
		return
	}

	if od.Status == order_status {
		err = gerror.New("订单状态已修改")

		return
	}

	// 插入订单日志
	orderlog := &entity.UsercenterOrderLog{
		Orderno:   od.SysOrderno,
		CreatedBy: 0,
		Event:     "上游回调",
		LogType:   2,
		OrderId:   od.Id,
		CreatedAt: gtime.Now(),
		ViewPaht:  "/usercenterOrder/index",
		Parame:    string(gjson.MustEncode(g.Map{"route": "/userLogs/usercenterUpserveLog/index", "query": g.Map{"oid": od.SysOrderno}})),
	}

	_ = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, _ = dao.UsercenterOrderLog.Ctx(ctx).TX(tx).OmitEmpty().Data(orderlog).Insert()
		// 总是返回nil,即使发生错误
		return nil
	})

	// 更新成功率
	// 先来更新比树和成功率
	err = userver.CommonService().PaymentOrderUpdate(ctx, od, order_status, utr, "上游回调", "System", 0)
	if err != nil {
		err = gerror.Wrap(err, err.Error())
		service.PayApi().LogNotify(ctx, "回调失败", out_trade_no.(string), od.UserOrderno, 1, 1, uonst.ERRO, err, req)
		return
	}

	service.PayApi().LogNotify(ctx, "回调成功", out_trade_no.(string), od.UserOrderno, 1, 1, uonst.ERRO, err, req)
	res = &payapi.Notifyoup{}
	res.Status = 1
	res.Message = "SUCCESS"

	return
	// response.RText(g.RequestFromCtx(ctx), "success")

}

func (d *Jjpay) NeiQueryorder(ctx context.Context, qudao gdb.Record, od *entity.UsercenterOrder) (res *payapi.QueryorderOup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["order_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// 示例数据

	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"orderNo": od.SysOrderno,
	}

	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/queryorder", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/queryorder", sfdata)

	if err != nil {
		err = gerror.New("通道响应错误")
		return
	}
	defer response.Close()
	// 读取响应内容
	content := response.ReadAllString()
	fmt.Printf("请求详细%v", content)
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		return
	}

	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		return

	}
	code := result["code"].(float64)
	fmt.Printf("看一下查询结果%v", code)
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		return
	}
	// var payurl, tradeNo string
	var Ttradestatus, Utr string
	var CompleteTime gtime.Time

	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 获取 payurl
		if data["status"].(string) == "created" || data["status"].(string) == "paying" {
			Ttradestatus = consts.WAITING_PAYMENT
		} else if data["status"].(string) == "success" {
			Ttradestatus = consts.PAYMENT_SUCCESS
		} else {
			Ttradestatus = consts.PAYMENT_FAILURE
		}
		CompleteTime = *od.UpdatedAt
		if utrVal, ok := data["utr"]; ok {
			if utrStr, isString := utrVal.(string); isString {
				Utr = utrStr

			}
		}
	} else {
		err = gerror.New("data 字段不存在或解析失败")
		return
	}

	// 初始化 res

	res = &payapi.QueryorderOup{}

	res.ApplyTime = od.CreatedAt
	res.MerchantTradeno = od.UserOrderno
	res.Money = gconv.String(fmt.Sprintf("%.2f", od.Money))

	res.CompleteTime = &CompleteTime
	res.Tradeno = od.SysOrderno
	res.Ttradestatus = Ttradestatus
	res.Utr = Utr

	return

}
func (d *Jjpay) NeiQuerywithdraw(ctx context.Context, qudao gdb.Record, od *entity.UsercenterPayment) (res *payapi.QueryorderOup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(qudao["setting_array"].String()), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" || PayConfig["payment_passage_code"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// var payurl, tradeNo string
	var Ttradestatus, Utr string
	var CompleteTime gtime.Time

	// 示例数据
	if od.Status == 0 {

		// 构造请求创建 sfdata 的 map
		sfdata := g.Map{

			"orderNo": strings.TrimSpace(od.SysOrderno),
		}

		nonce := generateRandomString(6)
		startTime := gtime.Now()
		// 带超时时间设置的请求方式 r
		// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
		response, nere := g.Client().Header(map[string]string{
			"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
			"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
			"nonce":        nonce,                                // 添加 Content-Type 头
			"sign":         generateSignature("POST", "/api/payorder/queryorder", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
			"Content-Type": "application/json",
		}).Post(ctx, d.GetBaseUrl()+"/api/payorder/queryorder", sfdata)

		if nere != nil {
			err = gerror.Wrap(nere, "通道响应错误")
			return

		}
		fmt.Printf("要查询的代付订单号%v", od.SysOrderno)
		defer response.Close()
		// 如果请求响应状态不为200
		if response.StatusCode != http.StatusOK {
			err = gerror.New("请求上游状态不成功")
			return
		}
		fmt.Printf("错误内容第一步-------4")
		// 读取响应内容
		content := response.ReadAllString()
		// 使用 map[string]interface{} 来解析不确定的 JSON 结构
		var result map[string]interface{}
		err = json.Unmarshal([]byte(content), &result)
		if err != nil {
			err = gerror.New("解析 JSON 错误:")
			return

		}
		code := result["code"].(float64)

		msg, ok := result["message"].(string)
		if !ok || msg == "" {
			msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
		}
		if code != 200 {
			err = gerror.New(msg)
			return
		}

		// 获取 data 中的内容
		if data, ok := result["result"].(map[string]interface{}); ok {
			// 获取 payurl
			// 获取 payurl
			if data["status"].(string) == "created" || data["status"].(string) == "paying" {
				Ttradestatus = consts.WAITING_PAYMENT
			} else if data["status"].(string) == "success" {
				Ttradestatus = consts.PAYMENT_SUCCESS
			} else {
				Ttradestatus = consts.PAYMENT_FAILURE
			}
			if od.Status == 3 {
				Ttradestatus = consts.WAITING_WITHDRAWAL
			}
			CompleteTime = *od.UpdatedAt
			if utrVal, ok := data["utr"]; ok {
				if utrStr, isString := utrVal.(string); isString {
					Utr = utrStr

				}
			}
		} else {
			err = gerror.New("data 字段不存在或解析失败")
			return
		}

	} else {
		if od.Status == 1 {
			Ttradestatus = consts.WITHDRAWAL_SUCCESS

		} else if od.Status == 2 {
			Ttradestatus = consts.WITHDRAWAL_FAILURE
		} else if od.Status == 3 {
			Ttradestatus = consts.WAITING_WITHDRAWAL
		}
		Utr = od.Utr
	}
	if err != nil {
		err = gerror.New("支付模块失败")
		return
	}
	// 初始化 res

	res = &payapi.QueryorderOup{}

	res.ApplyTime = od.CreatedAt
	res.MerchantTradeno = od.UserOrderno
	res.Money = gconv.String(fmt.Sprintf("%.2f", od.Money))

	res.CompleteTime = &CompleteTime
	res.Tradeno = od.SysOrderno
	res.Ttradestatus = Ttradestatus
	res.Utr = Utr

	return

}
func (d *Jjpay) QueryUPI(ctx context.Context, payconfig *entity.UsercenterPayConfig, upi string) (res *payapi.QueryUPIOup, err error) {

	PayConfig := make(map[string]string)
	fmt.Printf("payconfig详情: %+v\n", payconfig)
	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}
	fmt.Printf("pay进入dsconfig%v\n", payconfig)
	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(payconfig.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}

	// 构造请求到上游
	// 构造请求创建 sfdata 的 map
	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"upi": upi,
	}
	// 对 sfdata 按键名进行排序
	keys := make([]string, 0, len(sfdata))
	for k := range sfdata {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/queryupi", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/queryupi", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")

		return
	}
	// 求签名

	defer response.Close()
	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		return
	}
	// 读取响应内容
	content := response.ReadAllString()
	fmt.Printf("upi查询上游Uforpay结果: %s\n", content)
	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	fmt.Printf("查询结果%v", content)
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		return

	}
	code := result["code"].(float64)
	fmt.Printf("看一下查询结果%+v", result)
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		return
	}
	res = &payapi.QueryUPIOup{}
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 获取 payurl
		if data["status"].(string) == "exist" {
			res.IsSuccess = true

		} else {
			res.IsSuccess = false
		}
	} else {
		err = gerror.New("data 字段不存在或解析失败")
		return
	}

	return

}
func (d *Jjpay) QueryUTR(ctx context.Context, payconfig *entity.UsercenterPayConfig, utr string) (res *payapi.QueryUTROup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}
	fmt.Printf("pay进入dsconfig%v\n", payconfig)
	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(payconfig.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// 构造请求创建 sfdata 的 map
	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{

		"utr": utr,
	}
	// 对 sfdata 按键名进行排序
	keys := make([]string, 0, len(sfdata))
	for k := range sfdata {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/queryutr", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/queryutr", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")

		return
	}
	// 求签名

	defer response.Close()

	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		return
	}
	// 读取响应内容
	content := response.ReadAllString()
	fmt.Printf("utr查询上游Uforpay结果: %s\n", content)
	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		return

	}
	code := result["code"].(float64)
	fmt.Printf("看一下查询结果%+v", result)
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		return
	}
	res = &payapi.QueryUTROup{}
	// 获取 data 中的内容
	if data, ok := result["result"].(map[string]interface{}); ok {
		// 检查 utr 字段是否存在
		if utrVal, exists := data["utr"]; exists {
			if utrStr, ok := utrVal.(string); ok {
				res.Utr = utrStr
			}
		}

		// 检查 amount 字段是否存在
		if amountVal, exists := data["amount"]; exists {
			res.UtrAmount = gconv.Float64(amountVal)
		}

		// 检查 status 字段是否存在
		if statusVal, exists := data["status"]; exists {
			fmt.Printf("状态值类型: %T, 值: %v\n", statusVal, statusVal)

			// 先进行类型断言
			if statusStr, ok := statusVal.(string); ok {
				fmt.Printf("转换后的状态值: %s\n", statusStr)
				// 根据 utr_status 状态判断赋值
				switch strings.ToLower(statusStr) {
				case "waitmakeup":
					res.UtrStatus = "已确认收款"
					res.UtrStatusText = consts.UTR_CONFIRMED
				case "makeuped":
					res.UtrStatus = "已被领取"
					res.UtrStatusText = consts.UTR_RECEIVED
				case "notexist":
					res.UtrStatus = "utr不存在"
					res.UtrStatusText = consts.UTR_NOTFOUND
				default:
					fmt.Printf("未匹配的状态值: %s\n", statusStr)
					res.UtrStatus = "未知状态"
					res.UtrStatusText = consts.UTR_UNKNOWN
				}
			} else {
				fmt.Printf("状态值类型断言失败: %v\n", statusVal)
				res.UtrStatus = "状态格式错误"
				res.UtrStatusText = consts.UTR_UNKNOWN
			}
		} else {
			fmt.Printf("status 字段不存在\n")
		}

		// 检查 orderNo 字段是否存在
		if orderNoVal, exists := data["orderNo"]; exists {
			if orderNoStr, ok := orderNoVal.(string); ok {
				res.OrderNo = orderNoStr
			}
		}

		// 设置 Result 字段
		if messageVal, exists := data["message"]; exists {
			if messageStr, ok := messageVal.(string); ok {
				res.Result = messageStr
			}
		}
	}

	return
}
func (d *Jjpay) ReplenishOrder(ctx context.Context, payconfig *entity.UsercenterPayConfig, order *entity.UsercenterOrder, utr string) (res *payapi.ReplenishOrderOup, err error) {

	PayConfig := make(map[string]string)

	// 定义一个结构体来解析 setting_array
	type KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}
	fmt.Printf("pay进入dsconfig%v\n", payconfig)
	var settingArray []KeyValue
	// 解析 JSON 字符串到 settingArray 切片
	err = json.Unmarshal([]byte(payconfig.SettingArray), &settingArray)
	if err != nil {
		err = gerror.New("渠道配置错，误请联系管理员")
		return
	}

	// 将 settingArray 中的 key-value 转换为新的 map
	for _, kv := range settingArray {
		PayConfig[kv.Key] = kv.Value
	}
	if PayConfig["accessKey"] == "" || PayConfig["accessSecret"] == "" {
		err = gerror.New("渠道参数错误，误请联系管理员")
		return
	}
	// 构造请求到上游
	// 构造请求创建 sfdata 的 map
	// 构造请求创建 sfdata 的 map
	sfdata := g.Map{
		"OrderNo": order.SysOrderno,

		"utr": utr,
	}
	// 求签名
	// 对 sfdata 按键名进行排序
	keys := make([]string, 0, len(sfdata))
	for k := range sfdata {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	nonce := generateRandomString(6)
	startTime := gtime.Now()
	// 带超时时间设置的请求方式 response, err := g.Client().Timeout(10*time.Second).Get(ctx, whoisApi+ip)
	response, err := g.Client().Header(map[string]string{
		"accessKey":    gconv.String(PayConfig["accessKey"]), // 添加 Authorization 头
		"timestamp":    gconv.String(startTime.Unix()),       // 添加 Content-Type 头
		"nonce":        nonce,                                // 添加 Content-Type 头
		"sign":         generateSignature("POST", "/api/order/makeup", PayConfig["accessKey"], PayConfig["accessSecret"], gconv.String(startTime.Unix()), nonce),
		"Content-Type": "application/json",
	}).Post(ctx, d.GetBaseUrl()+"/api/order/makeup", sfdata)
	if err != nil {
		err = gerror.New("通道响应错误")

		return
	}
	// 求签名

	defer response.Close()

	// 如果请求响应状态不为200
	if response.StatusCode != http.StatusOK {
		err = gerror.New("请求上游状态不成功")
		return
	}
	// 读取响应内容
	content := response.ReadAllString()
	fmt.Printf("上游content%v\n", content)
	// 使用 map[string]interface{} 来解析不确定的 JSON 结构
	var result map[string]interface{}
	err = json.Unmarshal([]byte(content), &result)
	if err != nil {
		err = gerror.New("解析 JSON 错误:")
		return

	}
	code := result["code"].(float64)
	fmt.Printf("看一下查询结果%+v", result)
	msg, ok := result["message"].(string)
	if !ok || msg == "" {
		msg = "渠道上游未知错误" // 如果没有 "msg" 字段或 msg 为空，则设置默认值
	}
	if code != 200 {
		err = gerror.New(msg)
		return
	}
	res = &payapi.ReplenishOrderOup{}
	// 获取 data 中的内容
	res.Utr = utr
	res.UserOrder = order.UserOrderno
	res.SysOrderNo = order.SysOrderno
	res.Money = gconv.String(order.Money)
	res.Status = result["result"].(string)
	// 将字符串时间转换为 gtime.Time 对象
	timeStr := result["time"].(string)
	timeObj := gtime.New(timeStr)
	res.CreateTime = timeObj
	res.UpdateTime = timeObj
	res.ReplenishStatus = consts.REPL_SUCCESS

	return

}
