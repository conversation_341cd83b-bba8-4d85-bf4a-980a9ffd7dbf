import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';

// 从环境变量获取时区配置
const TIMEZONE = (import.meta.env as any).VITE_TIMEZONE || 'Asia/Kolkata';

/**
 * 将时间戳转换为指定时区对应的后端时间
 * @param timestamp 毫秒时间戳
 * @returns 转换后的时间戳
 */
export function convertTimestampForKolkata(timestamp: number): number {
  if (!timestamp) return timestamp;
  
  // 创建本地时间的Date对象
  const localDate = new Date(timestamp);
  
  // 将本地时间转换为指定时区的UTC时间
  const timezoneUtc = zonedTimeToUtc(localDate, TIMEZONE);
  
  return timezoneUtc.getTime();
}

/**
 * 将指定时区时间戳转换为本地时间，用于显示
 * @param timestamp 毫秒时间戳
 * @returns 转换后的时间戳
 */
export function convertTimestampFromKolkata(timestamp: number): number {
  if (!timestamp) return timestamp;
  
  // 将UTC时间转换为指定时区的本地时间
  const timezoneTime = utcToZonedTime(new Date(timestamp), TIMEZONE);
  
  return timezoneTime.getTime();
}

/**
 * 格式化为指定时区的日期字符串
 * @param timestamp 毫秒时间戳
 * @returns 格式化后的日期字符串
 */
export function formatKolkataTime(timestamp: number): string {
  if (!timestamp) return '';
  
  const timezoneTime = utcToZonedTime(new Date(timestamp), TIMEZONE);
  return timezoneTime.toLocaleString('en-IN', {
    timeZone: TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

/**
 * 处理日期范围的时区转换
 * @param dateRange 日期范围数组 [start, end]
 * @returns 转换后的日期范围
 */
export function convertDateRangeForKolkata(dateRange: [number, number] | null): [number, number] | null {
  if (!dateRange || !Array.isArray(dateRange) || dateRange.length !== 2) {
    return null;
  }

  const [start, end] = dateRange;
  return [
    start ? convertTimestampForKolkata(start) : start,
    end ? convertTimestampForKolkata(end) : end
  ];
}

/**
 * 将时间戳转换为加尔各答时区的日期字符串 (YYYY-MM-DD)
 * 专门用于处理只需要日期部分的字段，如 reportDate
 * @param timestamp 毫秒时间戳
 * @returns YYYY-MM-DD 格式的日期字符串
 */
export function convertTimestampToKolkataDate(timestamp: number): string {
  if (!timestamp) return '';

  // 将时间戳转换为加尔各答时区的时间
  const timezoneTime = utcToZonedTime(new Date(timestamp), TIMEZONE);

  // 格式化为 YYYY-MM-DD
  const year = timezoneTime.getFullYear();
  const month = String(timezoneTime.getMonth() + 1).padStart(2, '0');
  const day = String(timezoneTime.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}