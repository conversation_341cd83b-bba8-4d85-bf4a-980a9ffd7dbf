<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" :title="t('addons.usercenter.usercenterUserReport.title')">
        <!--  这是由系统生成的CURD表格，你可以将此行注释改为表格的描述 -->
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicForm  ref="searchFormRef" @register="register" @submit="reloadTable" @reset="reloadTable" @keyup.enter="reloadTable">
        <template #statusSlot="{ model, field }">
          <n-input v-model:value="model[field]" />
        </template>
      </BasicForm>
      <BasicTable  ref="actionRef" openChecked :columns="columns" :single-line="false"   :request="loadDataTable" :row-key="(row) => row.id" :actionColumn="actionColumn" :scroll-x="scrollX" :resizeHeightOffset="-10000"  :checked-row-keys="checkedIds" @update:checked-row-keys="handleOnCheckedRow" :summary="summary" @update:sorter="handleUpdateSorter">
        <template #tableTitle>
          <n-button type="primary"  @click="addTable" class="min-left-space" v-if="hasPermission(['/usercenter/usercenterUserReport/edit'])"> 
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            {{ t('addons.usercenter.usercenterUserReport.add') }}
          </n-button>
          <n-button type="error" @click="handleBatchDelete" class="min-left-space" v-if="hasPermission(['/usercenter/usercenterUserReport/delete'])">
            <template #icon>
              <n-icon>
                <DeleteOutlined />
              </n-icon>
            </template>
            {{ t('addons.usercenter.usercenterUserReport.batchDelete') }}
          </n-button>
          <n-button type="primary" @click="handleExport" class="min-left-space" v-if="hasPermission(['/usercenter/usercenterUserReport/export'])">
            <template #icon>
              <n-icon>
                <ExportOutlined />
              </n-icon>
            </template>
            {{ t('addons.usercenter.usercenterUserReport.export') }}
          </n-button>
        </template>
      </BasicTable>
    </n-card>
    <Edit ref="editRef" @reloadTable="reloadTable" />
    <View ref="viewRef" />
  </div>
</template>

<script lang="ts" setup>
  import { h, reactive, ref, computed } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { useI18n } from 'vue-i18n';
  import { BasicTable, TableAction } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { usePermission } from '@/hooks/web/usePermission';
  import { List, Export, Delete } from '@/api/addons/usercenter/usercenterUserReport';
  import { PlusOutlined, ExportOutlined, DeleteOutlined } from '@vicons/antd';
  import { createColumns, schemas, State } from './model';
  import { adaTableScrollX } from '@/utils/hotgo';
  import Edit from './edit.vue';
  import View from './view.vue';
  import type { ActionItem } from '@/components/Table';

  const dialog = useDialog();
  const message = useMessage();
  const { t } = useI18n();
  const { hasPermission } = usePermission();
  const actionRef = ref();
  const searchFormRef = ref<any>({});
  const editRef = ref();
  const viewRef = ref();
  const checkedIds = ref([]);

  // 使用工厂函数创建列配置
  const columns = createColumns(t);

  const actionColumn = reactive({
    width: 100,
    title: t('addons.usercenter.usercenterUserReport.operation'),
    key: 'action',
    fixed: 'right',
    render(record: State) {
      return h(TableAction as any, {
        style: 'button',
        actions: [],
        // actions: [
        //   {
        //     label: '编辑',
        //     onClick: handleEdit.bind(null, record),
        //     auth: ['/usercenter/usercenterUserReport/edit'],
        //   },

        //   {
        //     label: '删除',
        //     onClick: handleDelete.bind(null, record),
        //     auth: ['/usercenter/usercenterUserReport/delete'],
        //   },
        // ],
        dropDownActions: [
          {
            label: t('addons.usercenter.usercenterUserReport.viewDetails'),
            key: 'view',
            auth: ['/usercenter/usercenterUserReport/view'],
          },
        ],
        select: (key) => {
          if (key === 'view') {
            return handleView(record);
          }
        },
      });
    },
  });
  const statistic = ref({
    dsNum:0,
    dsSuccess:0,
    dsCgl:0,
    dsMoney:0,
    dsSuccessMoney:0,
    dsFee:0,
    dfNum:0,
    dfSuccess:0,
    dfCgl:0,
    dfMoney:0,
    dfSuccessMoney:0,
    dfFee:0,
    dfDcl:0,
    dfYcl:0,
    dfDclNum:0,
  })
  const summary = (pageData) => {
    return {
      id: {
        value: h(
          'span',
          { style: { fontWeight:'bold',fontSize:'16px' } },
          t('addons.usercenter.usercenterUserReport.summaryText', {
            dsNum: statistic.value.dsNum,
            dsMoney: statistic.value.dsMoney,
            dsSuccessMoney: statistic.value.dsSuccessMoney,
            dsCgl: statistic.value.dsCgl,
            dfNum: statistic.value.dfNum,
            dfMoney: statistic.value.dfMoney,
            dfSuccessMoney: statistic.value.dfSuccessMoney,
            dfCgl: statistic.value.dfCgl
          })
        ),
        colSpan: 15
      },
    }
  }
  const scrollX = computed(() => {
    return adaTableScrollX(columns, actionColumn.width);
  });

  const [register, {}] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  // 加载表格数据
  const loadDataTable = async (res) => {
    console.log('请求参数:', res);
    var result = await List({ ...searchFormRef.value?.formModel, ...res });
    console.log('result',result)
    statistic.value = result;
 
    return result;
  };

  // 更新选中的行
  function handleOnCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  // 重新加载表格数据
  function reloadTable() {
    actionRef.value?.reload();
  }

  // 添加数据
  function addTable() {
    editRef.value.openModal(null);
  }

  // 编辑数据
  function handleEdit(record: Recordable) {
    editRef.value.openModal(record);
  }

  // 查看详情
  function handleView(record: Recordable) {
    viewRef.value.openModal(record);
  }

  // 单个删除
  function handleDelete(record: Recordable) {
    dialog.warning({
      title: t('addons.usercenter.usercenterUserReport.warning'),
      content: t('addons.usercenter.usercenterUserReport.confirmDelete'),
      positiveText: t('addons.usercenter.usercenterUserReport.confirm'),
      negativeText: t('addons.usercenter.usercenterUserReport.cancel'),
      onPositiveClick: () => {
        Delete(record).then((_res) => {
          message.success(t('addons.usercenter.usercenterUserReport.deleteSuccess'));
          reloadTable();
        });
      },
    });
  }

  // 批量删除
  function handleBatchDelete() {
    if (checkedIds.value.length < 1){
      message.error(t('addons.usercenter.usercenterUserReport.selectAtLeastOne'));
      return;
    }

    dialog.warning({
      title: t('addons.usercenter.usercenterUserReport.warning'),
      content: t('addons.usercenter.usercenterUserReport.confirmBatchDelete'),
      positiveText: t('addons.usercenter.usercenterUserReport.confirm'),
      negativeText: t('addons.usercenter.usercenterUserReport.cancel'),
      onPositiveClick: () => {
        Delete({ id: checkedIds.value }).then((_res) => {
          checkedIds.value = [];
          message.success(t('addons.usercenter.usercenterUserReport.deleteSuccess'));
          reloadTable();
        });
      },
    });
  }

  // 导出
 function handleExport() {
    message.loading(t('addons.usercenter.usercenterUserReport.exporting'), { duration: 1200 });
    const params = {
      ...searchFormRef.value?.formModel,
      ...(checkedIds.value.length > 0 ? { ids: checkedIds.value } : {})
    };
    Export(params);
  }

  
  // 排序
  function handleUpdateSorter(sorters) {
      let params = {}
      if(sorters.order){
          searchFormRef.value.formModel = {
              ...searchFormRef.value.formModel,
              order: sorters.order === 'ascend' ? 'asc' : 'desc',
              columnKey: sorters.columnKey
          }
      }else{
          delete searchFormRef.value.formModel.order
          delete searchFormRef.value.formModel.columnKey
      }
      reloadTable()
  }

</script>

<style lang="less" scoped></style>