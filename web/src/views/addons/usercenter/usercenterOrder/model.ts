import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts } from '@/utils/dateUtil';
import { renderOptionTag } from '@/utils';
import { useDictStore } from '@/store/modules/dict';
import { NTag} from 'naive-ui';
import { renderPopoverMemberSumma, MemberSumma } from '@/utils';
const dict = useDictStore();

export class State {
  public id = 0; // id
  public userOrderno = ''; // 商户单号
  public sysOrderno = ''; // 系统单号
  public thirdOrderno = ''; // 三方单号
  public userId = 0; // 商户id
  public money = null; // 代收金额
  public status = 1; // 状态:0=待审,1=成功,2=失败
  public fee = null; // 手续费
  public callbackUrl = ''; // 回调地址
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间
  public ip = ''; // 创建ip
  public paytmId = 0; // 三方渠道id
  public isUp = 0; // 是否PF单
  public deletedAt = ''; // 删除时间
  public usercenterUserUsername= '';//用户名
  public createdBySumma?: null | MemberSumma = null; // 创建者id摘要信息
  public dealType = 0; // 成交类型
  public notifcStatus = 0; // 推送状态
  public utr = ''; // UTR
  public remark = ''; // 备注
  public upi = ''; // upi
  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state); 
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则
export const rules = {
  userOrderno: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: 'addons.usercenter.usercenterOrder.inputUserOrderno',
  },
  sysOrderno: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: 'addons.usercenter.usercenterOrder.inputSysOrderno',
  },
  thirdOrderno: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: 'addons.usercenter.usercenterOrder.inputThirdOrderno',
  },
  userId: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.inputUserId',
  },
  money: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.inputMoney',
  },
  status: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.selectStatus',
  },
  fee: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.inputFee',
  },
  callbackUrl: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: 'addons.usercenter.usercenterOrder.inputCallbackUrl',
  },
  ip: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: 'addons.usercenter.usercenterOrder.inputIp',
  },
  paytmId: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.inputPaytmId',
  },
  isUp: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.inputIsUp',
  },
  notifyStatus: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: 'addons.usercenter.usercenterOrder.selectNotifyStatus',
  },
};

// 表格搜索表单
export const schemas = ref<FormSchema[]>([
  {
    field: 'status',
    component: 'NSelect',
    label: 'addons.usercenter.usercenterOrder.status',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.selectStatus',
     
      options: (() => {
        const opts = dict.getOption('payment_status');
        console.log('opts',opts);
        return opts.value.map(item => ({
          ...item,
          value: String(Number(item.value) + 1)
        }));
      })(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'userOrderno',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.userOrderno',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputUserOrderno',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'sysOrderno',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.sysOrderno',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputSysOrderno',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdBy',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.operator',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputOperator',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  }, 
  {
    field: 'dealType',
    component: 'NSelect',
    label: 'addons.usercenter.usercenterOrder.dealType',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.selectDealType',
      options: [
        { label: 'addons.usercenter.usercenterOrder.systemDeal', value: 1 },
        { label: 'addons.usercenter.usercenterOrder.manualIntervention', value: 2 }
      ],
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: 'addons.usercenter.usercenterOrder.createdAt',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'updatedAt',
    component: 'NDatePicker',
    label: 'addons.usercenter.usercenterOrder.updatedAt',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'usercenterUserUsername',
    component: 'NRemoteSelect',
    label: 'addons.usercenter.usercenterOrder.user',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.selectUser',
      url:'/usercenter/usercenterUser/list',
      pk:'username',
      field:'username',
      onUpdateValue: (e: any) => {
        console.log(e);
      }
    },
  },
  {
    field: 'usercenterPayConfigName',
    component: 'NRemoteSelect',
    label: 'addons.usercenter.usercenterOrder.channel',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.selectChannel',
      url:'/usercenter/usercenterPayConfig/list',
      pk:'name',
      field:'name',
      onUpdateValue: (e: any) => {
        console.log(e);
      }
    },
  },
  {
    field:'money',
    component: 'NBetween',
    label:'addons.usercenter.usercenterOrder.money',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputMoney',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'utr',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.utr',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputUtr',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'upi',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.upi',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputUpi',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'notifcStatus',
    component: 'NSelect',
    label: 'addons.usercenter.usercenterOrder.notifcStatus',
    
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.selectNotifcStatus',
      options: [
        { label: 'addons.usercenter.usercenterOrder.pendingCallback', value: 1 },
        { label: 'addons.usercenter.usercenterOrder.callbackInProgress', value: 2 },
        { label: 'addons.usercenter.usercenterOrder.callbackSuccess', value: 3 },
        { label: 'addons.usercenter.usercenterOrder.callbackFailed', value: 4 }
      ],
    },
  },
  {
    field: 'remark',
    component: 'NInput',
    label: 'addons.usercenter.usercenterOrder.remark',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterOrder.inputRemark',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
]);
// 创建日志列配置的工厂函数
export const createColumnsLogs = (t: (key: string) => string) => [
  {
    title: t('addons.usercenter.usercenterOrder.createdAt'),
    key: 'createdAt',
    align: 'left',
    width: 200,
  },
  {
    title: t('addons.usercenter.usercenterOrder.orderno'),
    key: 'orderno',
    align: 'left',
    width: -1,
    ellipsis: true,
  },
  {
    title: t('addons.usercenter.usercenterOrder.operator'),
    key: 'createdBy',
    align: 'left',
    width: -1,
    render(row: State) {
      return row.createdBySumma ? renderPopoverMemberSumma(row.createdBySumma) : t('addons.usercenter.usercenterOrder.systemOperation');
    },
  },
  {
    title: t('addons.usercenter.usercenterOrder.event'),
    key: 'event',
    align: 'left',
    width: -1,
  },
  {
    title: t('addons.usercenter.usercenterOrder.orderType'),
    key: 'logType', 
    align: 'left',
    width: -1,
    render(row) {
      const typeMap = {
        1: { text: t('addons.usercenter.usercenterOrder.collection'), type: 'error' },
        2: { text: t('addons.usercenter.usercenterOrder.payment'), type: 'success' }
      };
      const status = typeMap[row.logType] || { text: t('addons.usercenter.usercenterOrder.unknown'), type: 'default' };
      return h(
        NTag,
        {
          type: status.type,
          size: 'small'
        },
        {
          default: () => status.text
        }
      );
    }
  },
  {
    title: t('addons.usercenter.usercenterOrder.remark'),
    key: 'remark',
    align: 'left',
    width: -1,
  },
];
// 创建主表列配置的工厂函数
export const createColumns = (t: (key: string) => string) => [
  {
    title: 'ID',
    key: 'id',
    align: 'left',
    width: -1,
  },
  {
    title: t('addons.usercenter.usercenterOrder.userOrderno'),
    key: 'userOrderno',
    align: 'left',
    width: -1,
    ellipsis: true,
    resizable: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.sysOrderno'),
    key: 'sysOrderno',
    align: 'left',
    width: -1,
    ellipsis: true,
    resizable: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.thirdOrderno'),
    key: 'thirdOrderno',
    align: 'left',
    width: -1,
    ellipsis: true,
    resizable: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.username'),
    key: 'usercenterUserUsername',
    align: 'left',
    width: -1,
    render(row: State) {
      return h(
        NTag,
        { type: 'info', round: false ,bordered: true,size: 'small',},
        { default: () => row.usercenterUserUsername}
      );
    },
    resizable: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.money'),
    key: 'money',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.status'),
    key: 'status',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('payment_status', row.status);
    },
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.fee'),
    key: 'fee',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.callbackUrl'),
    key: 'callbackUrl',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.createdAt'),
    key: 'createdAt',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.updatedAt'),
    key: 'updatedAt',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.ip'),
    key: 'ip',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.createdBy'),
    key: 'createdBy',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
  },
  {
    title: t('addons.usercenter.usercenterOrder.notifcStatus'),
    key: 'notifcStatus',
    align: 'left',
    width: -1,
    render(row: State) {
      const statusMap = {
        0: { text: t('addons.usercenter.usercenterOrder.pendingCallback'), type: 'warning' },
        1: { text: t('addons.usercenter.usercenterOrder.callbackInProgress'), type: 'info' },
        2: { text: t('addons.usercenter.usercenterOrder.callbackSuccess'), type: 'success' },
        3: { text: t('addons.usercenter.usercenterOrder.callbackFailed'), type: 'error' }
      };
      const status = statusMap[row.notifcStatus] || { text: t('addons.usercenter.usercenterOrder.unknownStatus'), type: 'default' };
      return h(
        NTag,
        {
          type: status.type,
          round: false,
          bordered: false,
          size: 'small',
          style: {
            fontWeight: 'bold'
          }
        },
        {
          default: () => status.text
        }
      );
    },
  },
  {
    title: t('addons.usercenter.usercenterOrder.channelName'),
    key: 'usercenterPayConfigName',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.remark'),
    key: 'remark',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.utr'),
    key: 'utr',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
  {
    title: t('addons.usercenter.usercenterOrder.upi'),
    key: 'upi',
    align: 'left',
    width: -1,
    resizable: true,
    ellipsis: true
  },
];
// 测试更新 123456
// 加载字典数据选项
export function loadOptions() {
  dict.loadOptions(['sys_normal_disable','payment_status']);
}