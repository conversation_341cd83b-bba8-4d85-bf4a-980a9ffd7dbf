import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts } from '@/utils/dateUtil';
import { renderOptionTag } from '@/utils';
import { useDictStore } from '@/store/modules/dict';
import { NTag} from 'naive-ui';
const dict = useDictStore();

export class State {
  public id = 0; // 日志ID
  public tid = ''; // 商户订单id
  public oid = ''; // 系统订单id
  public levelFormat = null; // 日志级别
  public content = ''; // 请求内容
  public requestContent = ''; // 响应内容
  public stack = null; // 打印堆栈
  public type = null; // 类型
  public line = ''; // 调用行
  public triggerNs = 0; // 请求耗时(秒)
  public status = 1; // 状态
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 修改时间
  public qudaoname = ''; // 渠道名称
  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则 - 使用翻译键，会被BasicForm自动翻译
export const rules = {
  tid: {
    required: true,
    trigger: ['blur', 'input'],
    message: 'addons.usercenter.usercenterUprequestLog.inputTid',
  },
  oid: {
    required: true,
    trigger: ['blur', 'input'],
    message: 'addons.usercenter.usercenterUprequestLog.inputOid',
  },
  type: {
    required: true,
    trigger: ['blur', 'input'],
    message: 'addons.usercenter.usercenterUprequestLog.inputType',
  },
  line: {
    required: true,
    trigger: ['blur', 'input'],
    message: 'addons.usercenter.usercenterUprequestLog.inputLine',
  },
  status: {
    required: true,
    trigger: ['blur', 'input'],
    message: 'addons.usercenter.usercenterUprequestLog.inputStatus',
  },
};

// 表格搜索表单 - 直接使用翻译键，Form组件会自动翻译
export const schemas = ref<FormSchema[]>([
  {
    field: 'tid',
    component: 'NInput',
    label: 'addons.usercenter.usercenterUprequestLog.tid',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.inputTid',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'oid',
    component: 'NInput',
    label: 'addons.usercenter.usercenterUprequestLog.oid',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.inputOid',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'status',
    component: 'NSelect',
    label: 'addons.usercenter.usercenterUprequestLog.status',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.selectStatus',
      options: dict.getOption('sys_normal_disable'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'type',
    component: 'NSelect',
    label: 'addons.usercenter.usercenterUprequestLog.type',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.selectType',
      options: (() => {
        const opts = dict.getOption('payment_type');
        return opts.value.map(item => ({
          ...item,
          value: String(Number(item.value) + 1)
        }));
      })(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: 'addons.usercenter.usercenterUprequestLog.createdAt',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'payConfigId',
    component: 'NRemoteSelect',
    label: 'addons.usercenter.usercenterUprequestLog.channel',
    defaultValue: null,
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.selectChannel',
      url:'/usercenter/usercenterPayConfig/list',
      pk:'id',      // 使用 id 作为 value
      field:'name', // 显示 name 作为 label
      onUpdateValue: (e: any) => {
        console.log('渠道选择器值变化 (ID):', e);
      }
    },
  },
  {
    field: 'line',
    component: 'NInput',
    label: 'addons.usercenter.usercenterUprequestLog.line',
    componentProps: {
      placeholder: 'addons.usercenter.usercenterUprequestLog.inputLine',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
]);

// 表格列工厂函数
export const createColumns = (t: (key: string) => string) => [
  {
    title: t('addons.usercenter.usercenterUprequestLog.id'),
    key: 'id',
    align: 'left',
    width: -1,
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.channelName'),
    key: 'qudaoname',
    align: 'left',
    width: -1,
    render(row: State) {
      return h(
        NTag,
        { type: 'info', round: false ,bordered: true,size: 'small',},
        { default: () => row.qudaoname } // 使用 `qudaoname` 渲染到 Tag 组件
      );
    },
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.tid'),
    key: 'tid',
    align: 'left',
    width: -1,
    ellipsis: true,
  },
  
  {
    title: t('addons.usercenter.usercenterUprequestLog.oid'),
    key: 'oid',
    align: 'center',
    width: -1,
    ellipsis: true,
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.type'),
    key: 'type',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('payment_type', row.type);
    },
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.levelFormat'),
    key: 'levelFormat',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('sys_log_type', row.levelFormat);
    },
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.status'),
    key: 'status',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('request_stauts', row.status);
    },
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.line'),
    key: 'line',
    align: 'left',
    width: -1,
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.triggerNs'),
    key: 'triggerNs',
    align: 'left',
    width: -1,
  },
  
  {
    title: t('addons.usercenter.usercenterUprequestLog.createdAt'),
    key: 'createdAt',
    align: 'left',
    width: -1,
  },
  {
    title: t('addons.usercenter.usercenterUprequestLog.updatedAt'),
    key: 'updatedAt',
    align: 'left',
    width: -1,
  },
];

// 加载字典数据选项
export function loadOptions() {
  dict.loadOptions(['request_stauts', 'sys_log_type', 'sys_switch', 'payment_type']);
  
}